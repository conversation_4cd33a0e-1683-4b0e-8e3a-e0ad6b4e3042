# Odoo Commands Cheat Sheet

This document contains essential commands for Odoo development, server management, and database operations.

## Server Management

### Starting the Odoo Server

```bash
# Basic server start
./odoo-bin

# Start with specific configuration file
./odoo-bin -c /path/to/config/file.conf

# Start with specific database
./odoo-bin -d database_name

# Start with developer mode
./odoo-bin --dev=all

# Start with specific port (default is 8069)
./odoo-bin --http-port=8080

# Start with specific addons path
./odoo-bin --addons-path=/path/to/addons

# Quick start with default options (automatically detects modules in current directory)
./odoo-bin start
```

### Server Options

```bash
# Display help and available options
./odoo-bin --help

# List available commands
./odoo-bin --help

# Display specific command help
./odoo-bin <command> --help
```

## Database Management

### Creating a Database

```bash
# Create a new database
./odoo-bin -d new_database_name -i base

# Create a database with demo data
./odoo-bin -d new_database_name -i base --without-demo=False

# Create a database with specific language
./odoo-bin -d new_database_name -i base --load-language=fr_FR
```

### Database Operations

```bash
# List databases
./odoo-bin db list

# Backup a database (ZIP format)
./odoo-bin db dump database_name --format=zip > backup.zip

# Restore a database
./odoo-bin db load --force database_name path/to/backup.zip

# Drop a database
./odoo-bin db drop database_name

# Duplicate a database
./odoo-bin db duplicate source_db target_db

# Rename a database
./odoo-bin db rename old_name new_name
```

## Module Management

### Installing Modules

```bash
# Install a module
./odoo-bin -d database_name -i module_name

# Install multiple modules
./odoo-bin -d database_name -i module1,module2,module3

# Update a module
./odoo-bin -d database_name -u module_name

# Update multiple modules
./odoo-bin -d database_name -u module1,module2,module3

# Update all modules
./odoo-bin -d database_name -u all
```

### Module Development

```bash
# Create a new module scaffold
./odoo-bin scaffold module_name /path/to/addons

# Generate module documentation
./odoo-bin cloc -p /path/to/module
```

## Shell and Development Tools

```bash
# Start an interactive Odoo shell
./odoo-bin shell -d database_name

# Count lines of code in modules
./odoo-bin cloc -d database_name

# Generate TypeScript definitions
./odoo-bin tsconfig
```

## Translation Management

```bash
# Export translations
./odoo-bin -d database_name --translate-out=path/to/file.po --language=fr_FR

# Import translations
./odoo-bin -d database_name --translate-in=path/to/file.po --language=fr_FR

# Update translations for specific modules
./odoo-bin -d database_name --translate-modules=module1,module2 --translate-out=path/to/file.po
```

## Configuration File Options

Common options to include in your Odoo configuration file:

```ini
[options]
; Server
admin_passwd = admin_password
db_host = localhost
db_port = 5432
db_user = odoo
db_password = password
http_port = 8069
addons_path = /path/to/addons1,/path/to/addons2

; Logging
logfile = /path/to/logfile.log
log_level = info

; Performance
workers = 4
max_cron_threads = 2

; Development
dev_mode = all
```

## Docker Commands (if using Docker)

```bash
# Run Odoo in Docker
docker run -d -p 8069:8069 --name odoo odoo:18.0

# Run with a volume for custom addons
docker run -d -p 8069:8069 -v /path/to/addons:/mnt/extra-addons --name odoo odoo:18.0

# Run with a PostgreSQL container
docker run -d -e POSTGRES_USER=odoo -e POSTGRES_PASSWORD=odoo -e POSTGRES_DB=postgres --name db postgres:15
docker run -d -p 8069:8069 --name odoo --link db:db -e DB_USER=odoo -e DB_PASSWORD=odoo odoo:18.0
```

## Common Debugging Options

```bash
# Enable all developer features
./odoo-bin --dev=all

# Enable specific developer features
./odoo-bin --dev=xml,reload,qweb

# Debug mode with increased log level
./odoo-bin --log-level=debug

# Debug with pdb support
./odoo-bin --dev=all --log-level=debug
```

## Database Connection String

```
# Format
postgresql://username:password@host:port/database

# Example
postgresql://odoo:odoo@localhost:5432/my_database
```

## Common Errors and Solutions

- **Could not connect to database**: Check PostgreSQL is running and credentials are correct
- **Addons path error**: Ensure the addons path exists and is correctly specified
- **Permission denied**: Check file permissions for the Odoo user
- **Port already in use**: Change the HTTP port or stop the process using the current port

Remember to replace placeholders like `database_name`, `module_name`, etc., with your actual values when using these commands.


./odoo-bin scaffold hello_world ./addons


python3 ./odoo-bin -c temp_odoo.conf -d admin -i my_library --http-port=8080

python3 odoo-bin -c " odoo18.conf" --dev=all


python3 odoo-bin -c " odoo18.conf" --dev=all^C