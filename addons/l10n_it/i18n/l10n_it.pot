# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_it
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-16 11:45+0000\n"
"PO-Revision-Date: 2024-01-16 11:45+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_it
#: model:ir.model,name:l10n_it.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_it
#: model:ir.model,name:l10n_it.model_account_report_expression
msgid "Accounting Report Expression"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_D_ATT
msgid "Accruals and deferrals - Assets"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_E_PASS
msgid "Accruals and deferrals - Liabilities"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_3b
msgid "Agricultural businesses (art.34)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover_section_2
#: model:account.report.line,name:l10n_it.tax_report_line_turnover_section_2
msgid ""
"Agricultural taxable transactions (Article 34 paragraph 1) and commercial "
"and professional taxable transactions"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat
msgid "Annual Tax Report"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_comp_fram
msgid "Annual tax for completed schedules"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_VIII
msgid "Article operations 7 to 7-septies without right to deduction"
msgstr ""

#. module: l10n_it
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_va
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_ve
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_vf
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_vh
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_vj
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_vl
#: model:account.report.column,name:l10n_it.tax_monthly_report_vat_balance
#: model:account.report.column,name:l10n_it.tax_report_vat_balance
msgid "Balance"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_saldi_riporti_e_interessi
#: model:account.report.line,name:l10n_it.tax_report_line_saldi_riporti_e_interessi
msgid "Balances, carryovers and interest"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_dag
msgid "Business Information"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vari_communi_perd
msgid "Changes in reporting periodicals"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_IMPEGNI
msgid "Commitments"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover_section_1
#: model:account.report.line,name:l10n_it.tax_report_line_turnover_section_1
msgid ""
"Contributions of agricultural products and transfers from exempt farmers (in"
" case of exceeding 1/3"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_C_ATT
msgid "Current assets"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_D_PASS
msgid "Debts"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29_I
msgid "Depreciable assets"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_IV
msgid "Depreciable assets and transfers interiors exempt"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_comp_fram_1
msgid "Determination of VAT due or credit for the tax period"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_comp_fram_3
msgid ""
"Determination of debit or credit VAT relating to all activities carried out"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va5_section_1
msgid "Equipment purchases"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_3a
msgid "Exempt operations"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_VII
msgid "Exempt operations art. 19, co. 3, letter. a-bis) and d-bis)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_III
msgid "Exempt operations referred to in art. 10, n. 27-quinquies"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_II
msgid ""
"Exempt operations referred to in nos. from 1 to 9 of the art. 10 not "
"included in their own business of the company or ancillary to taxable "
"operations"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_I
msgid ""
"Exempt transactions relating to gold from investments made by the subjects "
"referred to in the art. 19, co. 3, letter. d)"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields,field_description:l10n_it.field_account_tax__l10n_it_exempt_reason
msgid "Exoneration"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields,help:l10n_it.field_account_tax__l10n_it_exempt_reason
msgid "Exoneration type"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_E_PL
msgid "Extraordinary income and expenses"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_C_PL
msgid "Financial income and expenses"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_B_ATT
msgid "Fixed assets"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_dag_section1
msgid "General analytical data"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29_III
msgid "Goods intended for resale or for the production of goods and services"
msgstr ""

#. module: l10n_it
#. odoo-python
#: code:addons/l10n_it/models/account_tax.py:0
msgid ""
"If the tax amount is 0%, you must enter the exoneration code and the related"
" law reference."
msgstr ""

#. module: l10n_it
#: model:ir.model,name:l10n_it.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields,field_description:l10n_it.field_account_tax__l10n_it_law_reference
msgid "Law Reference"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va5_section_2
msgid "Management services"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_monthly_report_vat
msgid "Monthly VAT Report"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29_II
msgid "Non-depreciable capital goods"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_IX
msgid "Operations exempted by law no. 178/2020"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_V
msgid "Operations not subject"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_VI
msgid "Operations not subject to art. 74, co. 1"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover_section_4
#: model:account.report.line,name:l10n_it.tax_report_line_turnover_section_4
msgid "Other operations"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29_IV
msgid "Other purchases and imports"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op
msgid "Passive operations and VAT deduction allowed"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30_I
msgid "Periodic VAT due"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30_II
msgid "Periodic VAT paid"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30_III
msgid "Periodic VAT paid following notification of irregularities"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30_IV
msgid "Periodic VAT paid following payment orders"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_comp_fram_2
msgid "Previous year credit"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_B_PL
msgid "Production costs"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_B_PASS
msgid "Provisions for risks and charges"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_1
msgid "Purchases (Domestic, intra-Community and imports)"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_A_ATT
msgid "Receivables from shareholders"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_reverse_charge_iva
#: model:account.report.line,name:l10n_it.tax_report_line_reverse_charge_iva
msgid "Reverse Charge"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_RISCHI
msgid "Risks"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_C_PASS
msgid "Severance pay"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_A_PASS
msgid "Shareholders' Equity"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_3c
msgid "Special cases"
msgstr ""

#. module: l10n_it
#. odoo-python
#: code:addons/l10n_it/models/account_tax.py:0
msgid "Split Payment is not compatible with exoneration of kind 'N6'"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_dag_section2
msgid "Summary data for all activities"
msgstr ""

#. module: l10n_it
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_tax_va
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_tax_ve
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_tax_vf
#: model:ir.model,name:l10n_it.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_operazione_imponibile
#: model:account.report.line,name:l10n_it.tax_report_line_operazione_imponibile
msgid "Taxable transaction"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_BENI
msgid "Third party assets"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_2
msgid ""
"Total purchases and imports, total tax, purchases intra-community, imports "
"and purchases"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover_section_3
#: model:account.report.line,name:l10n_it.tax_report_line_turnover_section_3
msgid "Total taxable income and tax"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover
#: model:account.report.line,name:l10n_it.tax_report_line_turnover
msgid "Turnover"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_va
msgid "VA VAT Report"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va1
msgid ""
"VA1 - To be filled in by the entity of originator in cases of extraordinary "
"transactions"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va16
msgid ""
"VA11 - Data on amounts suspended as a result of the health emergency by "
"COVID-19"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va12
msgid ""
"VA12 - Reserved for indication of surplus credit of former parent companies "
"to be guaranteed"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va13
msgid "VA13 - Transactions carried out with respect to condominiums"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va5
msgid ""
"VA5 - Terminals for mobile telecommunication radio service with more than "
"50% deduction"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_iva
#: model:account.report.line,name:l10n_it.tax_report_line_iva
msgid "VAT"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_report_vat
msgid "VAT Report"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_conto_corrente_iva
#: model:account.report.line,name:l10n_it.tax_report_line_conto_corrente_iva
msgid "VAT account"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_4
msgid "VAT allowed as deduction"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va1_4
msgid "VAT/2023 declaration credit transferred"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_ve
msgid "VE VAT Report"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve1
#: model:account.report.line,name:l10n_it.tax_report_line_ve1
msgid ""
"VE1 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 2%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve10
#: model:account.report.line,name:l10n_it.tax_report_line_ve10
msgid ""
"VE10 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 10%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve11
#: model:account.report.line,name:l10n_it.tax_report_line_ve11
msgid ""
"VE11 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 12,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve2
#: model:account.report.line,name:l10n_it.tax_report_line_ve2
msgid ""
"VE2 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 4%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve20
#: model:account.report.line,name:l10n_it.tax_report_line_ve20
msgid "VE20 - Taxable transactions rate 4%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve21
#: model:account.report.line,name:l10n_it.tax_report_line_ve21
msgid "VE21 - Taxable transactions rate 5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve22
#: model:account.report.line,name:l10n_it.tax_report_line_ve22
msgid "VE22 - Taxable transactions rate 10%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve23
#: model:account.report.line,name:l10n_it.tax_report_line_ve23
msgid "VE23 - Taxable transactions rate 22%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve24
#: model:account.report.line,name:l10n_it.tax_report_line_ve24
msgid "VE24 - Total lines VE1 to VE11 and lines VE20 to VE23"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve25
#: model:account.report.line,name:l10n_it.tax_report_line_ve25
msgid "VE25 - Variations and rounding (use +/− sign)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve26
#: model:account.report.line,name:l10n_it.tax_report_line_ve26
msgid "VE26 - Total VE24 and VE25"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve3
#: model:account.report.line,name:l10n_it.tax_report_line_ve3
msgid ""
"VE3 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 6,4%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30
#: model:account.report.line,name:l10n_it.tax_report_line_ve30
msgid "VE30 - Transactions that contribute to the formation of the ceiling"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_I
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_I
msgid "VE30_I - Total"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_ii
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_ii
msgid "VE30_II - Exports"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_iii
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_iii
msgid "VE30_III - Intra-Community supplies"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_iv
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_iv
msgid "VE30_IV - Transfers to San Marino"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_v
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_v
msgid "VE30_V - Assimilated operations"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve31
#: model:account.report.line,name:l10n_it.tax_report_line_ve31
msgid "VE31 - Non-taxable transactions as a result of declarations of intent"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve32
#: model:account.report.line,name:l10n_it.tax_report_line_ve32
msgid "VE32 - Other non-taxable transactions"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve33
#: model:account.report.line,name:l10n_it.tax_report_line_ve33
msgid "VE33 - Exempt transactions (art.10"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve34
#: model:account.report.line,name:l10n_it.tax_report_line_ve34
msgid ""
"VE34 - Transactions not subject to the tax under Articles 7 to 7-septies"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35
#: model:account.report.line,name:l10n_it.tax_report_line_ve35
msgid "VE35 - Transactions with application of internal reverse charge"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_I
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_I
msgid "VE35_I - Total"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_ii
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_ii
msgid "VE35_II - Disposal of scrap and other recovered materials"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_iii
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_iii
msgid "VE35_III - Disposals of pure gold and silver"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_iv
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_iv
msgid "VE35_IV - Subcontracting in the construction industry"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_ix
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_ix
msgid "VE35_IX - Energy sector operations"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_v
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_v
msgid "VE35_V - Disposal of capital buildings"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_vi
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_vi
msgid "VE35_VI - Disposal of cell phones"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_vii
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_vii
msgid "VE35_VII - Disposal of electronic products"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_viii
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_viii
msgid "VE35_VIII - Benefits construction and related industries"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve36
#: model:account.report.line,name:l10n_it.tax_report_line_ve36
msgid "VE36 - Non-taxable transactions made to earthquake victims"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve37
#: model:account.report.line,name:l10n_it.tax_report_line_ve37
msgid ""
"VE37 - Transactions made during the year but with tax due in subsequent "
"years"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve37_I
#: model:account.report.line,name:l10n_it.tax_report_line_ve37_I
msgid "VE37_I - Total"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve37_ii
#: model:account.report.line,name:l10n_it.tax_report_line_ve37_ii
msgid "VE37_II - ex art. 32-bis, DL no. 83/2012"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve38
#: model:account.report.line,name:l10n_it.tax_report_line_ve38
msgid "VE38 - Transactions with parties referred to in Article 17-ter."
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve39
#: model:account.report.line,name:l10n_it.tax_report_line_ve39
msgid ""
"VE39 - (minus) Transactions made in previous years but with tax due in 2022"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve4
#: model:account.report.line,name:l10n_it.tax_report_line_ve4
msgid ""
"VE4 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 7,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve40
#: model:account.report.line,name:l10n_it.tax_report_line_ve40
msgid "VE40 - (minus) Disposals of depreciable assets and internal transfers."
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve5
#: model:account.report.line,name:l10n_it.tax_report_line_ve5
msgid ""
"VE5 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 7,5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve45
msgid "VE50 - TURNOVER"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve6
#: model:account.report.line,name:l10n_it.tax_report_line_ve6
msgid ""
"VE6 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 8,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve7
#: model:account.report.line,name:l10n_it.tax_report_line_ve7
msgid ""
"VE7 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 8,5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve8
#: model:account.report.line,name:l10n_it.tax_report_line_ve8
msgid ""
"VE8 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 8,8%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve9
#: model:account.report.line,name:l10n_it.tax_report_line_ve9
msgid ""
"VE9 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 9,5%"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_vf
msgid "VF VAT Report"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF1
msgid "VF1 - compensation percentage 2%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF10
msgid "VF10 - compensation percentage 8,8%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF11
msgid "VF11 - compensation percentage 10%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF12
msgid "VF12 - compensation percentage 12,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF13
msgid "VF13 - compensation percentage 22%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF17
msgid ""
"VF17 - Purchases and imports without payment of tax, with use of the ceiling"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF18_II
msgid "VF18 - Exempt purchases and imports not subject to the tax"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF18_I
msgid ""
"VF18 - Other non-tax purchases, not subject to tax and relating to certain "
"special regimes"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF19
msgid ""
"VF19 - Purchases from subjects who have made use of concessional schemes"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF2
msgid "VF2 - compensation percentage 4%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF20
msgid ""
"VF20 - Purchases and imports not subject to the tax made by earthquake "
"victims"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF21
msgid ""
"VF21 - Purchases and imports for which the deduction is excluded or reduced "
"(art. 19-bis1)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF22
msgid "VF22 - Purchases and imports for which the deduction is not permitted"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF23
msgid ""
"VF23 - Purchases recorded in the year but with tax deduction deferred to "
"subsequent years"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF24
msgid "VF24 - Purchases recorded in previous years but with tax"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF25
msgid "VF25 - Total purchases and imports"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF26
msgid "VF26 - Tax variations and roundings (indicate with the +/- sign)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF27
msgid "VF27 - Total tax on taxable purchases and imports"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF28_II
msgid "VF28 - Imports"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF28_I
msgid "VF28 - Intra-community purchases"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF28_III
msgid "VF28 - Purchases from San Marino"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29
msgid "VF29 - Total distribution of purchases and imports"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF3
msgid "VF3 - compensation percentage 5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF31
msgid "VF31 - Purchases intended for occasional taxable transactions"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34
msgid "VF34 - Data for calculating the deduction percentage"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF35
msgid "VF35 - VAT not paid on purchases and imports indicated"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF36
msgid ""
"VF36 - VAT deductible for gold purchases made by parties other than "
"producers and transformers pursuant to art. 19, paragraph 5 bis"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF37
msgid "VF37 - VAT allowed as deduction"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF38
msgid "VF38 - Reserved for mixed agricultural enterprises"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF39
msgid "VF39 - compensation percentage 2%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF4
msgid "VF4 - compensation percentage 6,4%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF40
msgid "VF40 - compensation percentage 4%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF41
msgid "VF41 - compensation percentage 6.4%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF42
msgid "VF42 - compensation percentage 7%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF43
msgid "VF43 - compensation percentage 7,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF44
msgid "VF44 - compensation percentage 7,5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF45
msgid "VF45 - compensation percentage 8,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF46
msgid "VF46 - compensation percentage 8,5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF47
msgid "VF47 - compensation percentage 9.5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF48
msgid "VF48 - compensation percentage 10%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF49
msgid "VF49 - compensation percentage 12,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF5
msgid "VF5 - compensation percentage 7%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF51
msgid "VF51 - Tax variations and roundings (indicate with the +/- sign)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF52
msgid "VF52 - TOTALS Algebraic sum of lines from VF39 to VF51"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF53
msgid ""
"VF53 - Deductible VAT charged to the operations referred to in line VF38"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF54
msgid ""
"VF54 - Deductible amount for transfers, including intra-community, of "
"agricultural products referred to in art. 34"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF55
msgid "VF55 - TOTAL VAT allowed as deduction"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF6
msgid "VF6 - compensation percentage 7,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF62
msgid "VF62 - Reserved for agricultural businesses"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF7
msgid "VF7 - compensation percentage 7,5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF70
msgid "VF70 - TOTAL adjustments (indicate with the +/- sign)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF71
msgid "VF71 - VAT allowed as deduction"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF8
msgid "VF8 - compensation percentage 8,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF9
msgid "VF9 - compensation percentage 8,5%"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_vh
msgid "VH VAT Report"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh1
msgid "VH1 - January"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh10
msgid "VH10 - August"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh11
msgid "VH11 - September"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh12
msgid "VH12 - QUARTER III"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh13
msgid "VH13 - October"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh14
msgid "VH14 - November"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh15
msgid "VH15 - December"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh16
msgid "VH16 - QUARTER IV"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh17
msgid "VH17 - Deposit due"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh2
msgid "VH2 - February"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh3
msgid "VH3 - March"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh4
msgid "VH4 - QUARTER I"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh5
msgid "VH5 - April"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh6
msgid "VH6 - May"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh7
msgid "VH7 - June"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh8
msgid "VH8 - QUARTER II"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh9
msgid "VH9 - July"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_vj
msgid "VJ VAT Report"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj1
#: model:account.report.line,name:l10n_it.tax_report_line_vj1
msgid "VJ1 - Purchases of goods from Vatican City and San Marino"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj10
#: model:account.report.line,name:l10n_it.tax_report_line_vj10
msgid "VJ10 - Imports of scrap and other recovered materials"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj11
#: model:account.report.line,name:l10n_it.tax_report_line_vj11
msgid "VJ11 - Imports of industrial gold and pure silver"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj12
#: model:account.report.line,name:l10n_it.tax_report_line_vj12
msgid "VJ12 - Subcontracting of services in the construction field"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj13
#: model:account.report.line,name:l10n_it.tax_report_line_vj13
msgid ""
"VJ13 - Purchases of buildings or portions of buildings used for capital "
"purposes"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj14
#: model:account.report.line,name:l10n_it.tax_report_line_vj14
msgid "VJ14 - Purchases of cell phones"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj15
#: model:account.report.line,name:l10n_it.tax_report_line_vj15
msgid "VJ15 - Purchases of electronic products"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj16
#: model:account.report.line,name:l10n_it.tax_report_line_vj16
msgid "VJ16 - Provision of services in the construction field"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj17
#: model:account.report.line,name:l10n_it.tax_report_line_vj17
msgid "VJ17 - Purchases of energy sector goods and services"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj18
#: model:account.report.line,name:l10n_it.tax_report_line_vj18
msgid "VJ18 - Purchases made by VAT-registered public administrations"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj19
#: model:account.report.line,name:l10n_it.tax_report_line_vj19
msgid "VJ19 - Total frame VJ"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj2
#: model:account.report.line,name:l10n_it.tax_report_line_vj2
msgid "VJ2 - Extraction of goods from VAT warehouses"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj3
#: model:account.report.line,name:l10n_it.tax_report_line_vj3
msgid ""
"VJ3 - Purchases of goods already in Italy or services, from non-residents"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj4
#: model:account.report.line,name:l10n_it.tax_report_line_vj4
msgid ""
"VJ4 - Fees paid to resellers of travel tickets and resellers of parking "
"documents"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj5
#: model:account.report.line,name:l10n_it.tax_report_line_vj5
msgid "VJ5 - Commissions paid by travel agents to their intermediaries"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj6
#: model:account.report.line,name:l10n_it.tax_report_line_vj6
msgid "VJ6 - Purchases of scrap and other recovered materials"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj7
#: model:account.report.line,name:l10n_it.tax_report_line_vj7
msgid "VJ7 - Purchases of industrial gold and pure silver made in Italy"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj8
#: model:account.report.line,name:l10n_it.tax_report_line_vj8
msgid "VJ8 - Investment gold purchases made in Italy"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj9
#: model:account.report.line,name:l10n_it.tax_report_line_vj9
msgid "VJ9 - Intra-EU Purchases of Goods"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_vl
msgid "VL VAT Report"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl1
msgid "VL1 - VAT payable"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl10
msgid "VL10 - Non-transferable credit surplus"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl11
msgid ""
"VL11 - Credits art. 8, paragraph 6-quater, Presidential Decree. n. 322/98"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl12
msgid "VL12 - Periodic payments omitted"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl2
msgid "VL2 - VAT deductible"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl20
msgid "VL20 - Interim refunds required"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl21
msgid "VL21 - Amount of credits transferred"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl22
msgid "VL22 - VAT credit resulting from the first 3 quarters"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl23
msgid "VL23 - Interest due for quarterly payments"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl24
msgid "VL24 - Previous year transfers returned by the parent company"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl25
msgid "VL25 - Previous year credit surplus"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl26
msgid ""
"VL26 - Credit requested for reimbursement in previous years computable as a "
"deduction following refusal by the office"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl27
msgid ""
"VL27 - Tax credits used in periodic payments and for the advance payment"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl28
msgid ""
"VL28 - Credits received from savings management companies used in periodic "
"payments and for the advance payment"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl29
msgid "VL29 - EU car payments relating to sales carried out during the year"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl3
msgid "VL3 - Tax Due"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30
msgid "VL30 - Periodic VAT amount"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl31
msgid "VL31 - Amount of debts transferred"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl32
msgid "VL32 - VAT DUE"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl33
msgid "VL33 - VAT CREDIT"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl34
msgid "VL34 - Tax credits used in the annual return"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl35
msgid ""
"VL35 - Credits received from asset management companies used in the annual "
"declaration"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl36
msgid "VL36 - Interest due on the annual return"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl37
msgid ""
"VL37 - Credit transferred by savings management companies pursuant to art. 8"
" of the legislative decree n. 351/2001"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl38
msgid "VL38 - TOTAL VAT DUE"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl39
msgid "VL39 - TOTAL VAT INPUT"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl4
msgid "VL4 - Tax Credit"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl40
msgid "VL40 - Payments made following excess use of credit"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl41_II
msgid "VL41 - Difference between credit potential and actual credit"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl41_I
msgid "VL41 - Difference between periodic VAT due and periodic VAT paid"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl8
msgid "VL8 - Credit resulting from the previous year's declaration"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl9
msgid "VL9 - Credit offset in the template"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp10
#: model:account.report.line,name:l10n_it.tax_report_line_vp10
msgid "VP10 - EU car payments"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp11
#: model:account.report.line,name:l10n_it.tax_report_line_vp11
msgid "VP11 - Tax Credit"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp12
#: model:account.report.line,name:l10n_it.tax_report_line_vp12
msgid "VP12 - Interest due for quarterly settlements"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp13
#: model:account.report.line,name:l10n_it.tax_report_line_vp13
msgid "VP13 - Down payment due"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp14
#: model:account.report.line,name:l10n_it.tax_report_line_vp14
msgid "VP14 - VAT payable"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp14a
#: model:account.report.line,name:l10n_it.tax_report_line_vp14a
msgid "VP14a - VAT payable (debit)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp14b
#: model:account.report.line,name:l10n_it.tax_report_line_vp14b
msgid "VP14b - VAT payable (credit)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp2
#: model:account.report.line,name:l10n_it.tax_report_line_vp2
msgid "VP2 - Total active transactions"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp3
#: model:account.report.line,name:l10n_it.tax_report_line_vp3
msgid "VP3 - Total passive transactions"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp4
#: model:account.report.line,name:l10n_it.tax_report_line_vp4
msgid "VP4 - VAT due"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp5
#: model:account.report.line,name:l10n_it.tax_report_line_vp5
msgid "VP5 - VAT Deductible"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp6
#: model:account.report.line,name:l10n_it.tax_report_line_vp6
msgid "VP6 - VAT due"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp6a
#: model:account.report.line,name:l10n_it.tax_report_line_vp6a
msgid "VP6a - VAT due (payable)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp6b
#: model:account.report.line,name:l10n_it.tax_report_line_vp6b
msgid "VP6b - VAT due (credit)"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp7
#: model:account.report.line,name:l10n_it.tax_report_line_vp7
msgid "VP7 - Previous period debt not to exceed 25,82"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp8
#: model:account.report.line,name:l10n_it.tax_report_line_vp8
msgid "VP8 - Previous period credit"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp9
#: model:account.report.line,name:l10n_it.tax_report_line_vp9
msgid "VP9 - Previous year credit"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_D_PL
msgid "Value adjustments of financial assets and liabilities"
msgstr ""

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_A_PL
msgid "Value of production"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n1
msgid "[N1] Escluse ex art. 15"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n2_1
msgid ""
"[N2.1] Non soggette ad IVA ai sensi degli artt. Da 7 a 7-septies del DPR "
"633/72"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n2_2
msgid "[N2.2] Non soggette - altri casi"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n2
msgid "[N2] Non soggette"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_1
msgid "[N3.1] Non imponibili - esportazioni"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_2
msgid "[N3.2] Non imponibili - cessioni intracomunitarie"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_3
msgid "[N3.3] Non imponibili - cessioni verso San Marino"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_4
msgid ""
"[N3.4] Non imponibili - operazioni assimilate alle cessioni all'esportazione"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_5
msgid "[N3.5] Non imponibili - a seguito di dichiarazioni d'intento"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_6
msgid ""
"[N3.6] Non imponibili - altre operazioni che non concorrono alla formazione "
"del plafond"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3
msgid "[N3] Non imponibili"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n4
msgid "[N4] Esenti"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n5
msgid "[N5] Regime del margine / IVA non esposta in fattura"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_1
msgid ""
"[N6.1] Inversione contabile - cessione di rottami e altri materiali di "
"recupero"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_2
msgid "[N6.2] Inversione contabile - cessione di oro e argento puro"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_3
msgid "[N6.3] Inversione contabile - subappalto nel settore edile"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_4
msgid "[N6.4] Inversione contabile - cessione di fabbricati"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_5
msgid "[N6.5] Inversione contabile - cessione di telefoni cellulari"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_6
msgid "[N6.6] Inversione contabile - cessione di prodotti elettronici"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_7
msgid ""
"[N6.7] Inversione contabile - prestazioni comparto edile esettori connessi"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_8
msgid "[N6.8] Inversione contabile - operazioni settore energetico"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_9
msgid "[N6.9] Inversione contabile - altri casi"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6
msgid ""
"[N6] Inversione contabile (per le operazioni in reverse charge ovvero nei "
"casi di autofatturazione per acquisti extra UE di servizi ovvero per "
"importazioni di beni nei soli casi previsti)"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n7
msgid ""
"[N7] IVA assolta in altro stato UE (prestazione di servizi di "
"telecomunicazioni, tele-radiodiffusione ed elettronici ex art. 7-octies, "
"comma 1 lett. a, b, art. 74-sexies DPR 633/72)"
msgstr ""
