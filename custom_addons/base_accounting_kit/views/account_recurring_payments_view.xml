<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!--Recurring Templates Form view-->
    <record id="account_recurring_payments_view_form" model="ir.ui.view">
        <field name="name">account.recurring.payments.view.form</field>
        <field name="model">account.recurring.payments</field>
        <field name="arch" type="xml">
            <form string="Recurring Template">
                <header>
                    <field name="state" widget="statusbar" options="{'clickable': '1'}"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="pay_time"/>
                        </group>
                        <group>
                            <field name="recurring_period"/>
                            <field name="recurring_interval" class="o_address_zip"/>
                        </group>
                        <group>
                            <field name="partner_id" invisible="pay_time != 'pay_later'"/>
                            <field name="company_id" invisible="1"/>
                        </group>
                        <group>
                            <field name="date"/>
                            <field name="next_date"/>
                            <field name="amount"/>
                        </group>
                    </group>

                    <notebook>
                        <page name="accounting_info" string="Accounting Info">
                            <group>
                                <group>
                                    <field name="credit_account"/>
                                    <field name="debit_account"/>
                                </group>
                                <group>
                                    <field name="journal_id"/>
                                    <field name="journal_state"/>
                                </group>
                            </group>
                            <group>
                                <group>
                                    <field name="analytic_account_id"/>
                                </group>
                            </group>
                        </page>
                        <page name="other_info" string="Other Info">
                            <group>
                                <field name="description" placeholder="Description..." nolabel="1" colspan="4"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <!--Recurring Templates Tree View-->
    <record id="account_recurring_payments_view_tree" model="ir.ui.view">
        <field name="name">account.recurring.payments.view.tree</field>
        <field name="model">account.recurring.payments</field>
        <field name="arch" type="xml">
            <list string="Recurring Templates">
                <field name="name"/>
                <field name="partner_id"/>
                <field name="debit_account"/>
                <field name="credit_account"/>
                <field name="journal_id"/>
                <field name="date"/>
                <field name="amount"/>
                <field name="state"/>
            </list>
        </field>
    </record>
    <!--Action for Recurring Templates-->
    <record id="action_account_recurring_payments_view" model="ir.actions.act_window">
        <field name="name">Recurring Templates</field>
        <field name="res_model">account.recurring.payments</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="account_recurring_payments_view_tree"/>
        <field name="target">current</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">Click to create new recurring payment template</p>
        </field>
    </record>
    <!--Action for Recurring Templates MenuItem-->
    <menuitem id="account_recurring_payments_child1"
              name="Recurring Templates" groups="account.group_account_user"
              action="action_account_recurring_payments_view"
              parent="account.root_payment_menu"/>
</odoo>
