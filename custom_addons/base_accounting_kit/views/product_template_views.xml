<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Product Template -->
    <record id="product_template_form_view" model="ir.ui.view">
        <field name="name">product.template.view.form.inherit.base.accounting.kit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <field name="property_account_expense_id" position="after">
                <field name="asset_category_id"
                    domain="[('type', '=', 'purchase')]"
                    context="{'default_type': 'purchase'}"
                    groups="account.group_account_user"/>
            </field>
        </field>
    </record>
</odoo>
