<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
<!--  Account Bank Statement Form View  -->
    <record id="account_bank_statement_view_form"
            model="ir.ui.view">
        <field name="name">account.bank.statement.view.form</field>
        <field name="model">account.bank.statement</field>
        <field name="priority">100</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name" required="1"/>
                        <field name="balance_start"/>
                        <field name="balance_end_real"/>
                        <field name="attachment_ids" widget="many2many_binary"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
<!--  Action Account Bank Statement  -->
    <record id="action_bank_statement_create_form_bank_reconcile_widget"
            model="ir.actions.act_window">
        <!-- Binding removed since the same can be achieved with multi-edit -->
        <field name="name">Create Statement</field>
        <field name="res_model">account.bank.statement</field>
        <field name="view_mode">form</field>
        <field name="view_id"
               ref="account_bank_statement_view_form"/>
        <field name="target">new</field>
    </record>

</odoo>
