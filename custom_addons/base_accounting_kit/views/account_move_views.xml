<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Add "Asset Category" to supplier invoices -->
    <record model="ir.ui.view" id="view_move_form">
        <field name="name">account.move.view.form.inherit.base.accounting.kit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='account_id']" position="before">
                <field string="Asset Category" column_invisible="parent.move_type != 'in_invoice'"
                       name="asset_category_id" domain="[('type','=','purchase')]" context="{'default_type':'purchase'}"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']/list/field[@name='account_id']" position="after">
                <field string="Asset Category" name="asset_category_id" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='ref']" position="after">
                <field name="has_due" invisible="1"/>
                <field name="is_warning" invisible="1"/>
            </xpath>
            <xpath expr="//header" position="after">
                <div class="alert alert-info" role="alert" style="height: 40px; margin-bottom:10px;"
                     invisible="not has_due or move_type not in ['out_invoice','out_refund','out_receipt']">
                    This Customer's due amount is <strong><field name="due_amount"/></strong>.
                </div>
            </xpath>
            <xpath expr="//sheet/div[@name='button_box']" position="after">
                <div role="alert" class="alert alert-danger" style="height: 40px; margin-bottom:10px;"
                     invisible="not is_warning or move_type not in ['out_invoice','out_refund','out_receipt']">
                    This customer's <strong>warning limit</strong> has been crossed.
                </div>
            </xpath>
        </field>
    </record>
</odoo>
