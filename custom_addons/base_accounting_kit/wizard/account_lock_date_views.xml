<?xml version="1.0" encoding="UTF-8"?>
<odoo>
<!--Account Lock Date Form View-->
    <record model="ir.ui.view" id="account_lock_date_view_form">
        <field name="name">account.lock.date.view.form</field>
        <field name="model">account.lock.date</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="company_id" invisible="1"/>
                    <label for="sale_lock_date"/>
                    <div class="d-flex">
                        <field name="sale_lock_date" class="oe_inline"/><span class="text-muted"><i>to lock invoices</i></span>
                    </div>
                    <label for="purchase_lock_date"/>
                    <div class="d-flex">
                        <field name="purchase_lock_date" class="oe_inline"/><span class="text-muted"><i>to lock bills</i></span>
                    </div>
                    <field name="hard_lock_date" class="oe_inline"/>
                </group>
                <footer>
                    <button string="Update" name="execute" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
<!--Action Account Lock Date-->
    <record model="ir.actions.act_window" id="account_update_lock_date_act_window">
        <field name="name">Lock your Fiscal Period</field>
        <field name="res_model">account.lock.date</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
<!--Menu Lock Dates-->
    <menuitem id="actions_menu"
              name="Actions"
              sequence="500"
              parent="account.menu_finance_entries"
              groups="account.group_account_manager"/>
    <menuitem id="menu_lock_dates"
              name="Lock Dates"
              action="account_update_lock_date_act_window"
              parent="base_accounting_kit.actions_menu"
              groups="account.group_account_manager"/>
</odoo>
