<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <record model="res.groups" id="group_account_dashboard">
            <field name="name">Access to Accounting Dashboard</field>
        </record>

        <record id="account_asset_category_multi_company_rule" model="ir.rule">
            <field name="name">Account Asset Category multi-company</field>
            <field ref="model_account_asset_category" name="model_id"/>
            <field eval="True" name="global"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]
            </field>
        </record>

        <record id="account_asset_asset_multi_company_rule" model="ir.rule">
            <field name="name">Account Asset multi-company</field>
            <field ref="model_account_asset_asset" name="model_id"/>
            <field eval="True" name="global"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]
            </field>
        </record>
        <!--    Rename user group as Accountant    -->
        <record id="account.group_account_user" model="res.groups">
            <field name="name">Accountant</field>
            <field name="implied_ids" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="category_id" ref="base.module_category_accounting_accounting"/>
        </record>

        <!--    Rename user group as Chief Accountant    -->
        <record id="account.group_account_manager" model="res.groups">
            <field name="name">Chief Accountant</field>
            <field name="implied_ids"
                   eval="[(3, ref('account.group_account_invoice')), (4, ref('account.group_account_user'))]"/>
            <field name="category_id" ref="base.module_category_accounting_accounting"/>
        </record>
    </data>
</odoo>
