<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
<!--    Records for the account.financial.report model-->
        <record id="account_financial_report_cash_flow0" model="account.financial.report">
            <field name="name">Cash Flow Statement</field>
            <field name="type">sum</field>
        </record>
<!--    Defines a financial report record for operations -->
        <record id="account_financial_report_operation0"
            model="account.financial.report">
            <field name="name">Operations</field>
            <field name="sequence">1</field>
            <field name="parent_id" ref="account_financial_report_cash_flow0"/>
            <field name="display_detail">detail_with_hierarchy</field>
            <field name="type">sum</field>
        </record>
<!--    Cash in operation -->
        <record id="cash_in_from_operation0" model="account.financial.report">
            <field name="name">Cash In</field>
            <field name="sequence">1</field>
            <field name="parent_id" ref="account_financial_report_operation0"/>
            <field name="display_detail">detail_with_hierarchy</field>
            <field name="type">accounts</field>
        </record>
<!--    Cash out operation -->
        <record id="cash_out_operation1" model="account.financial.report">
            <field name="name">Cash Out</field>
            <field name="sequence">2</field>
            <field name="parent_id" ref="account_financial_report_operation0"/>
            <field name="display_detail">detail_with_hierarchy</field>
            <field name="type">accounts</field>
        </record>
<!--    Defines a financial report record for Investing Activities -->
        <record id="account_financial_report_investing_activity0" model="account.financial.report">
            <field name="name">Investing Activities</field>
            <field name="sequence">2</field>
            <field name="parent_id" ref="account_financial_report_cash_flow0"/>
            <field name="display_detail">detail_with_hierarchy</field>
            <field name="type">sum</field>
        </record>
<!--    Cash in Investing Activities -->
        <record id="cash_in_investing0" model="account.financial.report">
            <field name="name">Cash In</field>
            <field name="parent_id" ref="account_financial_report_investing_activity0"/>
            <field name="display_detail">detail_with_hierarchy</field>
            <field name="type">accounts</field>
        </record>
<!--    Cash out Investing Activities -->
        <record id="cash_out_investing1" model="account.financial.report">
            <field name="name">Cash Out</field>
            <field name="parent_id" ref="account_financial_report_investing_activity0"/>
            <field name="display_detail">detail_with_hierarchy</field>
            <field name="type">accounts</field>
        </record>
<!--    Defines a financial report record for Financing Activities -->
        <record id="account_financial_report_financing_activity1" model="account.financial.report">
            <field name="name">Financing Activities</field>
            <field name="sequence">3</field>
            <field name="parent_id" ref="account_financial_report_cash_flow0"/>
            <field name="display_detail">detail_with_hierarchy</field>
            <field name="type">sum</field>
        </record>
<!--    Cash in Financing Activities -->
        <record id="cash_in_financial0" model="account.financial.report">
            <field name="name">Cash In</field>
            <field name="parent_id" ref="account_financial_report_financing_activity1"/>
            <field name="display_detail">detail_with_hierarchy</field>
            <field name="type">accounts</field>
        </record>
<!--    Cash out Financing Activities -->
        <record id="cash_out_financial1" model="account.financial.report">
            <field name="name">Cash Out</field>
            <field name="parent_id" ref="account_financial_report_financing_activity1"/>
            <field name="display_detail">detail_with_hierarchy</field>
            <field name="type">accounts</field>
        </record>
    </data>
</odoo>
