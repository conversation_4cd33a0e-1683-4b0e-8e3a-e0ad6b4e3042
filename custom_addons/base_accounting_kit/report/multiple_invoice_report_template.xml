<odoo>
    <template id="report_multiple_invoice_new">
        <t t-call="base_accounting_kit.new_external_layout">
            <t t-set="o" t-value="o.with_context(lang=lang)"/>
            <t t-set="address">
                <address t-field="o.partner_id"
                         t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}'/>
                <div t-if="o.partner_id.vat" class="mt16">
                    <t t-if="o.company_id.country_id.vat_label"
                       t-esc="o.company_id.country_id.vat_label"
                       id="inv_tax_id_label"/>
                    <t t-else="">Tax ID</t>:
                    <span t-field="o.partner_id.vat"/>
                </div>
            </t>
            <div class="page">
                <t t-set="txt_style"
                   t-value="'font-size:25px; text-align:center;top:0px; left:15px; position:absolute; z-index:99;'"/>
                <t t-if="body_txt_position == 'tr'">
                    <t t-set="txt_style"
                       t-value="'font-size:25px; text-align:center;top:0px; right:15px; position:absolute; z-index:99;'"/>
                </t>
                <t t-if="body_txt_position == 'br'">
                    <t t-set="txt_style"
                       t-value="'font-size:25px; text-align:right;'"/>
                </t>
                <t t-if="body_txt_position == 'bl'">
                    <t t-set="txt_style"
                       t-value="'font-size:25px; text-align:left;'"/>
                </t>
                <h2>
                    <span t-if="o.move_type == 'out_invoice' and o.state == 'posted'">
                        Invoice
                    </span>
                    <span t-if="o.move_type == 'out_invoice' and o.state == 'draft'">
                        Draft Invoice
                    </span>
                    <span t-if="o.move_type == 'out_invoice' and o.state == 'cancel'">
                        Cancelled Invoice
                    </span>
                    <span t-if="o.move_type == 'out_refund'">Credit Note</span>
                    <span t-if="o.move_type == 'in_refund'">Vendor Credit Note
                    </span>
                    <span t-if="o.move_type == 'in_invoice'">Vendor Bill</span>
                    <span t-if="o.name != '/'" t-field="o.name"/>
                </h2>

                <div id="informations" class="row mt32 mb32">
                    <div class="col-auto col-3 mw-100 mb-2"
                         t-if="o.invoice_date" name="invoice_date">
                        <strong>Invoice Date:</strong>
                        <p class="m-0" t-field="o.invoice_date"/>
                    </div>
                    <div class="col-auto col-3 mw-100 mb-2"
                         t-if="o.invoice_date_due and o.move_type == 'out_invoice' and o.state == 'posted'"
                         name="due_date">
                        <strong>Due Date:</strong>
                        <p class="m-0" t-field="o.invoice_date_due"/>
                    </div>
                    <div class="col-auto col-3 mw-100 mb-2"
                         t-if="o.invoice_origin" name="origin">
                        <strong>Source:</strong>
                        <p class="m-0" t-field="o.invoice_origin"/>
                    </div>
                    <div class="col-auto col-3 mw-100 mb-2"
                         t-if="o.partner_id.ref" name="customer_code">
                        <strong>Customer Code:</strong>
                        <p class="m-0" t-field="o.partner_id.ref"/>
                    </div>
                    <div class="col-auto col-3 mw-100 mb-2" t-if="o.ref"
                         name="reference">
                        <strong>Reference:</strong>
                        <p class="m-0" t-field="o.ref"/>
                    </div>
                </div>
                <t t-set="display_discount"
                   t-value="any(l.discount for l in o.invoice_line_ids)"/>
                <table class="table table-sm o_main_table"
                       name="invoice_line_table">
                    <thead>
                        <tr>
                            <th name="th_description" class="text-left">
                                <span>Description</span>
                            </th>
                            <th name="th_quantity" class="text-right">
                                <span>Quantity</span>
                            </th>
                            <th name="th_priceunit"
                                t-attf-class="text-right {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                <span>Unit Price</span>
                            </th>
                            <th name="th_price_unit" t-if="display_discount"
                                t-attf-class="text-right {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                <span>Disc.%</span>
                            </th>
                            <th name="th_taxes"
                                t-attf-class="text-left {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                <span>Taxes</span>
                            </th>
                            <th name="th_subtotal" class="text-right">
                                <span>
                                    Amount
                                </span>

                            </th>
                        </tr>
                    </thead>
                    <tbody class="invoice_tbody">
                        <t t-set="current_subtotal" t-value="0"/>
                        <t t-set="lines"
                           t-value="o.invoice_line_ids.sorted(key=lambda l: (-l.sequence, l.date, l.move_name, -l.id), reverse=True)"/>
                        <t t-foreach="lines" t-as="line">
                            <t t-set="current_subtotal"
                               t-value="current_subtotal + line.price_subtotal"
                               groups="account.group_show_line_subtotals_tax_excluded"/>
                            <t t-set="current_subtotal"
                               t-value="current_subtotal + line.price_total"
                               groups="account.group_show_line_subtotals_tax_included"/>
                            <tr t-att-class="'bg-200 font-weight-bold o_line_section' if line.display_type == 'line_section' else 'font-italic o_line_note' if line.display_type == 'line_note' else ''">
                                <t t-if="line.display_type not in ('line_section', 'line_note')"
                                   name="account_invoice_line_accountable">
                                    <td name="account_invoice_line_name">
                                        <span t-field="line.name"
                                              t-options="{'widget': 'text'}"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.quantity"/>
                                        <span t-field="line.product_uom_id"
                                              groups="uom.group_uom"/>
                                    </td>
                                    <td t-attf-class="text-right {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                        <span class="text-nowrap"
                                              t-field="line.price_unit"/>
                                    </td>
                                    <td t-if="display_discount"
                                        t-attf-class="text-right {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                        <span class="text-nowrap"
                                              t-field="line.discount"/>
                                    </td>
                                    <td t-attf-class="text-left {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                        <span t-esc="', '.join(map(lambda x: (x.description or x.name), line.tax_ids))"
                                              id="line_tax_ids"/>
                                    </td>
                                    <td class="text-right o_price_total">
                                        <span class="text-nowrap"
                                              t-field="line.price_subtotal"
                                              />
                                    </td>
                                </t>
                                <t t-if="line.display_type == 'line_section'">
                                    <td colspan="99">
                                        <span t-field="line.name"
                                              t-options="{'widget': 'text'}"/>
                                    </td>
                                    <t t-set="current_section" t-value="line"/>
                                    <t t-set="current_subtotal" t-value="0"/>
                                </t>
                                <t t-if="line.display_type == 'line_note'">
                                    <td colspan="99">
                                        <span t-field="line.name"
                                              t-options="{'widget': 'text'}"/>
                                    </td>
                                </t>
                            </tr>

                            <t t-if="current_section and (line_last or lines[line_index+1].display_type == 'line_section')">
                                <tr class="is-subtotal text-right">
                                    <td colspan="99">
                                        <strong class="mr16">Subtotal</strong>
                                        <span
                                                t-esc="current_subtotal"
                                                t-options='{"widget":
                                                "monetary", "display_currency":
                                                o.currency_id}'
                                        />
                                    </td>
                                </tr>
                            </t>
                        </t>
                    </tbody>
                </table>
                <div class="clearfix">
                            <div id="right-elements" t-attf-class="#{'col-5' if report_type != 'html' else 'col-12 col-md-5'} ms-5 d-inline-block float-end">
                                <div id="total" class="clearfix row mt-n3">
                                    <div class="ms-auto">
                                        <table class="o_total_table table table-borderless avoid-page-break-inside">

                                            <!-- Tax totals summary (invoice currency) -->
                                            <t t-if="o.tax_totals" t-call="account.document_tax_totals">
                                                <t t-set="tax_totals" t-value="o.tax_totals"/>
                                                <t t-set="currency" t-value="o.currency_id"/>
                                            </t>

                                            <!--Payments-->
                                            <t t-if="print_with_payments">
                                                <t t-if="o.payment_state != 'invoicing_legacy'">
                                                    <t t-set="payments_vals" t-value="o.sudo().invoice_payments_widget and o.sudo().invoice_payments_widget['content'] or []"/>
                                                    <t t-foreach="payments_vals" t-as="payment_vals">
                                                        <tr t-if="payment_vals['is_exchange'] == 0">
                                                            <td>
                                                                <i class="oe_form_field text-end oe_payment_label">Paid on <t t-out="payment_vals['date']" t-options='{"widget": "date"}'>2021-09-19</t></i>
                                                            </td>
                                                            <td class="text-end">
                                                                <span t-out="payment_vals['amount']" t-options='{"widget": "monetary", "display_currency": o.currency_id}'>20.00</span>
                                                            </td>
                                                        </tr>
                                                    </t>
                                                    <t t-if="len(payments_vals) > 0">
                                                        <tr class="fw-bold">
                                                            <td>Amount Due</td>
                                                            <td class="text-end">
                                                                <span t-field="o.amount_residual">11.05</span>
                                                            </td>
                                                        </tr>
                                                    </t>
                                                </t>
                                            </t>
                                        </table>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <p class="text-end lh-sm" t-if="o.company_id.display_invoice_amount_total_words">
                                        Total amount in words: <br/>
                                        <small class="text-muted lh-sm"><span t-field="o.amount_total_words">Thirty one dollar and Five cents</span></small>
                                    </p>
                                </div>

                                <!-- Tax totals summary (company currency) -->
                                <t t-if="o.tax_totals.get('display_in_company_currency')">
                                    <t t-set="tax_totals" t-value="o.tax_totals"/>
                                    <t t-call="account.document_tax_totals_company_currency_template"/>
                                </t>
                                <t t-else="">
                                    <div class="oe_structure"/>
                                </t>
                            </div>

                </div>
                <p t-if="o.move_type in ('out_invoice', 'in_refund') and o.payment_reference"
                   name="payment_communication">
                    Please use the following communication for your payment :
                    <b>
                        <span t-field="o.payment_reference"/>
                    </b>
                </p>
                <p t-if="o.invoice_payment_term_id" name="payment_term">
                    <span t-field="o.invoice_payment_term_id.note"/>
                </p>
                <p t-if="o.narration" name="comment">
                    <span t-field="o.narration"/>
                </p>
                <p t-if="o.fiscal_position_id.note" name="note">
                    <span t-field="o.fiscal_position_id.note"/>
                </p>
                <p t-if="o.invoice_incoterm_id" name="incoterm">
                    <strong>Incoterm:</strong>
                    <span t-field="o.invoice_incoterm_id.code"/>
                    -
                    <span t-field="o.invoice_incoterm_id.name"/>
                </p>
                <div id="qrcode" t-if="o.display_qr_code">
                    <p t-if="qr_code_urls.get(o.id)">
                        <strong class="text-center">Scan me with your banking
                            app.
                        </strong>
                        <br/>
                        <br/>
                        <img class="border border-dark rounded"
                             t-att-src="qr_code_urls[o.id]"/>
                    </p>
                </div>
                <t t-if="mi_type == 'text'">
                    <div t-if="txt_position == 'body'" t-att-style="txt_style">
                        <span t-esc="mi.copy_name"/>
                    </div>
                </t>
            </div>
        </t>
    </template>

    <template id="report_multiple_invoice">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-set="lang"
                   t-value="o.invoice_user_id.sudo().lang if o.move_type in ('in_invoice', 'in_refund') else o.partner_id.lang"/>
                <t t-set="print_with_payments" t-value="True"/>
                <t t-if="o._get_name_invoice_report() == 'account.report_invoice_document'"
                   t-call="account.report_invoice_document" t-lang="lang"/>
                <t t-foreach="mi_ids" t-as="mi">
                    <t t-call="base_accounting_kit.report_multiple_invoice_new"
                       t-lang="lang"/>
                </t>

            </t>
        </t>
    </template>

    <record id="report_multiple_invoice_copies" model="ir.actions.report">
        <field name="name">Multiple Invoice Copies</field>
        <field name="model">account.move</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">base_accounting_kit.report_multiple_invoice</field>
        <field name="report_file">base_accounting_kit.report_multiple_invoice</field>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
