# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_accounting_kit
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-04 07:00+0000\n"
"PO-Revision-Date: 2021-02-04 07:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid " (grouped)"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__entry_count
msgid "# Asset Entries"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__depreciation_nbr
msgid "# of Depreciation Lines"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__installment_nbr
msgid "# of Installment Lines"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/credit_limit.py:0
#: code:addons/base_accounting_kit/models/credit_limit.py:0
#: code:addons/language_translation/base_accounting_kit/models/credit_limit.py:0
#: code:addons/language_translation/base_accounting_kit/models/credit_limit.py:0
#, python-format
msgid "%s is in  Blocking Stage and has a due amount of %s %s to pay"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
msgid ": Bank Book Report"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
msgid ": Cash Book Report"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
msgid ": Day Book Report"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid ": General ledger"
msgstr ": Grand livre général"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid ": Trial Balance"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid ""
"<br/>\n"
"                                    <strong>Date to :</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_kanban
msgid "<i class=\"fa fa-building\" role=\"img\" aria-label=\"Enterprise\"/>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
msgid "<span>Comp</span>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span>Pas due</span>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid "<strong>Balance</strong>"
msgstr "<strong>Équilibre</strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "<strong>Company:</strong>"
msgstr "<strong>Entreprise: </strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid "<strong>Credit</strong>"
msgstr "<strong>Crédit</strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "<strong>Date from :</strong>"
msgstr "<strong>Date du:</strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "<strong>Date to :</strong>"
msgstr "<strong>Date au:</strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid "<strong>Debit</strong>"
msgstr "<strong>Débit</strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "<strong>Display Account:</strong>"
msgstr "<strong>Afficher le compte:</strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "<strong>Display Account</strong>"
msgstr "<strong>Afficher le compte</strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>Entrées triées par:</strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "<strong>Journal:</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "<strong>Journals:</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid "<strong>Name</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "<strong>Purchase</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "<strong>Sorted By:</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "<strong>Target Moves:</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "<strong>Total</strong>"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_res_partner__warning_stage
#: model:ir.model.fields,help:base_accounting_kit.field_res_users__warning_stage
msgid ""
"A warning message will appear once the selected customer is crossed warning "
"amount. Set its value to 0.00 to disable this feature"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model,name:base_accounting_kit.model_account_account
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
#, python-format
msgid "Account"
msgstr "Compte"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.server,name:base_accounting_kit.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:base_accounting_kit.account_asset_cron
#: model:ir.cron,name:base_accounting_kit.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_bank_book_report
msgid "Account Bank Book Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_cash_book_report
msgid "Account Cash Book Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_common_account_report
msgid "Account Common Account Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__date
msgid "Account Date"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_day_book_report
msgid "Account Day Book Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_followup
msgid "Account Follow-up"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_print_journal
msgid "Account Print Journal"
msgstr "Impression de journal comptable"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_recurring_entries_line
msgid "Account Recurring Entries Line"
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.client,name:base_accounting_kit.action_account_invoice_report_all
#: model:ir.model,name:base_accounting_kit.model_account_financial_report
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__children_ids
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_tree
msgid "Account Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__account_report_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__account_report_id
#: model:ir.ui.menu,name:base_accounting_kit.menu_account_financial_reports_tree
msgid "Account Reports"
msgstr "Rapports de compte"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Account Total"
msgstr "Total du compte"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__type__account_type
msgid "Account Type"
msgstr "Type de compte"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__account_type_ids
msgid "Account Types"
msgstr "Types de comptes"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.res_config_settings_view_accounting_kit
msgid "Accounting"
msgstr "Comptabilité"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_form_view
msgid "Accounting Info"
msgstr "Informations comptables"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_recurring_payments
msgid "Accounting Recurring Payment"
msgstr "Paiement récurrent comptable"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_asset.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_asset.js:0
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr "Écritures comptables en attente de vérification manuelle"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__account_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__account_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__account_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__account_ids
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__type__accounts
msgid "Accounts"
msgstr "Comptes"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_needaction
msgid "Action Needed"
msgstr "Action nécessaire"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__active
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__active
msgid "Active"
msgstr "Active"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__active_limit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__active_limit
msgid "Active Credit Limit"
msgstr "Limite de crédit active"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Additional Options"
msgstr "Options additionelles"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_line_form
msgid "After"
msgstr "Après"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_aged_balance_view
#: model:ir.actions.report,name:base_accounting_kit.action_report_aged_partner_balance
#: model:ir.ui.menu,name:base_accounting_kit.menu_aged_trial_balance
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "Solde du partenaire âgé"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_agedpartnerbalance
msgid "Aged Partner Balance Report"
msgstr "Rapport sur le solde du partenaire âgé"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Aged Payable"
msgstr "Vieilli à payer"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Aged Receivable"
msgstr "Créance âgée"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_balance_report__display_account__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__display_account__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__display_account__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_account_report__display_account__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__display_account__all
msgid "All"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__target_move__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__target_move__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_day_book_report__target_move__all
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "All Entries"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__target_move__posted
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__target_move__posted
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_day_book_report__target_move__posted
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "All Posted Entries"
msgstr "Toutes les entrées publiées"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "All accounts"
msgstr "Tous les comptes"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "All accounts'"
msgstr "Tous les comptes"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__amount
#, python-format
msgid "Amount"
msgstr "Montant"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "Montant des lignes d'amortissement"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__installment_value
msgid "Amount of Installment Lines"
msgstr "Montant des lignes de versement"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Analytic Acc."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__account_analytic_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__analytic_account_id
msgid "Analytic Account"
msgstr "Compte analytique"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Analytic Tags."
msgstr "Balises analytiques."

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__asset_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__asset_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Asset"
msgstr "Atout"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__account_asset_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Asset Account"
msgstr "Compte d'actifs"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__asset_category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_invoice_asset_category
msgid "Asset Category"
msgstr "Catégorie d'actif"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "Durées des actifs à modifier"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__asset_end_date
msgid "Asset End Date"
msgstr "Date de fin de l'actif"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__asset_method_time
msgid "Asset Method Time"
msgstr "Temps de méthode d'actif"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__name
msgid "Asset Name"
msgstr "Nom de l'élément"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__asset_start_date
msgid "Asset Start Date"
msgstr "Date de début de l'élément"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__name
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_product__asset_category_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template__asset_category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Asset Type"
msgstr "Type d'actif"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:base_accounting_kit.menu_action_account_asset_asset_list_normal_purchase
msgid "Asset Types"
msgstr "Types d'actifs"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_asset_category
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__asset_category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_tree
msgid "Asset category"
msgstr "Catégorie d'actif"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr "Actif créé"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr "Ligne d'amortissement des actifs"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr "Actif vendu ou cédé. Écriture comptable en attente de validation."

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr "Reconnaissance des actifs / revenus"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_assets0
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_asset_asset_form
#: model:ir.ui.menu,name:base_accounting_kit.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:base_accounting_kit.menu_action_asset_asset_report
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Assets"
msgstr "Les atouts"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_asset_asset_report
#: model:ir.model,name:base_accounting_kit.model_asset_asset_report
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.action_account_asset_report_graph
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.action_account_asset_report_pivot
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "Analyse des actifs"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__asset_depreciation_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__asset_depreciation_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr "Lignes d'amortissement des actifs"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Actifs et revenus"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Assets in closed state"
msgstr "Actifs à l'état fermé"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Actifs à l'état brouillon et ouvert"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "Actifs à l'état de brouillon"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "Actifs en état de fonctionnement"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_reports_audit
msgid "Audit Reports"
msgstr "Rapports d'audit"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__open_asset
msgid "Auto-confirm Assets"
msgstr "Confirmation automatique des actifs"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__0
msgid "Automatic formatting"
msgstr "Formatage automatique"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "BANK AND CASH BALANCE"
msgstr "BANQUE ET SOLDE DE TRÉSORERIE"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "Balance"
msgstr "Équilibre"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:base_accounting_kit.action_balance_sheet_report
#: model:ir.ui.menu,name:base_accounting_kit._account_financial_reports_balance_sheet
msgid "Balance Sheet"
msgstr "Bilan"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_bank_book_menu
msgid "Bank Book"
msgstr "Livret de banque"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_bank_book_view
#: model:ir.actions.report,name:base_accounting_kit.action_report_bank_book
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_bank_book_form_view
msgid "Bank Book Report"
msgstr "Rapport de livre bancaire"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_matching.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_matching.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr "Rapprochement bancaire"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__bank_reference
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__bank_reference
msgid "Bank Reference"
msgstr "Référence bancaire"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Base Amount"
msgstr "Montant de base"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__blocking_stage
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__blocking_stage
msgid "Blocking Amount"
msgstr "Montant de blocage"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_bank_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_cash_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_day_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_update_lock_date_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "Annuler"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_res_partner__blocking_stage
#: model:ir.model.fields,help:base_accounting_kit.field_res_users__blocking_stage
msgid ""
"Cannot make sales once the selected customer is crossed blocking amount.Set "
"its value to 0.00 to disable this feature"
msgstr ""

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_cash_book_menu
msgid "Cash Book"
msgstr "Livre de caisse"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_cash_book_view
#: model:ir.actions.report,name:base_accounting_kit.action_report_cash_book
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_cash_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_day_book_form_view
msgid "Cash Book Report"
msgstr "Rapport de livre de caisse"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_cash_flow_report
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_cash_flow
msgid "Cash Flow Report"
msgstr "Rapport de flux de trésorerie"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_cash_flow0
#: model:ir.actions.act_window,name:base_accounting_kit.action_cash_flow_report
#: model:ir.actions.report,name:base_accounting_kit.action_report_cash_flow
#: model:ir.ui.menu,name:base_accounting_kit.menu_account_cash_flow_report
msgid "Cash Flow Statement"
msgstr "État des flux de trésorerie"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_account__cash_flow_type
msgid "Cash Flow type"
msgstr "Type de flux de trésorerie"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.cash_in_financial0
#: model:account.financial.report,name:base_accounting_kit.cash_in_from_operation0
#: model:account.financial.report,name:base_accounting_kit.cash_in_investing0
msgid "Cash In"
msgstr "Encaisser"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.cash_out_financial1
#: model:account.financial.report,name:base_accounting_kit.cash_out_investing1
#: model:account.financial.report,name:base_accounting_kit.cash_out_operation1
msgid "Cash Out"
msgstr "Cash Out"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Category"
msgstr "Catégorie"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Category of asset"
msgstr "Catégorie d'actif"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Check all"
msgstr "Vérifie tout"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__cheque_reference
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__cheque_reference
msgid "Cheque Reference"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__method
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__method_time
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_asset_depreciation_confirmation_wizard__date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_account_recurring_payments_view
msgid "Click to create new recurring payment template"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__state__close
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__asset_asset_report__state__close
#, python-format
msgid "Close"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Close statement"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Closed"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "Code"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__label_filter
msgid "Column Label"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__company_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Company"
msgstr "Société"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__name
msgid "Company Name"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.cash_flow_report_view
msgid "Comparison"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method
msgid "Computation Method"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Confirm"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_res_partner
msgid "Contact"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Create a counterpart"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Create model"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/asset_depreciation_confirmation_wizard.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Asset Moves"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/asset_depreciation_confirmation_wizard.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Revenue Moves"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__create_date
msgid "Created on"
msgstr "Créé le"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "Credit"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__credit_account
msgid "Credit Account"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_customer_form
msgid "Credit Limit"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__enable_credit_limit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__enable_credit_limit
msgid "Credit Limit Enabled"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__depreciated_value
msgid "Cumulative Depreciation"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__currency_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#, python-format
msgid "Currency"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Current"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__amount
msgid "Current Depreciation"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_config_settings__customer_credit_limit
msgid "Customer Credit Limit"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Customer Invoice"
msgstr "Facture client"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr ""

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_reports_daily_reports
msgid "Daily Reports"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: model:ir.ui.menu,name:base_accounting_kit.menu_accounting_dashboard
#, python-format
msgid "Dashboard"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__sortby__sort_date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__sortby__sort_date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_print_journal__sort_selection__date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__sortby__sort_date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__cash_flow_report__filter_cmp__filter_date
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#, python-format
msgid "Date"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__date_to_cmp
msgid "Date End"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__date_from_cmp
msgid "Date Start"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Date of asset"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.cash_flow_report_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.financial_report_wiz_modified
msgid "Dates"
msgstr ""

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_day_book_menu
msgid "Day Book"
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.report,name:base_accounting_kit.day_book_pdf_report
msgid "Day Book PDF Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_day_book_view
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_day_book_report_template
msgid "Day Book Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__recurring_period__days
msgid "Days"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "Debit"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__debit_account
msgid "Debit Account"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_product__deferred_revenue_category_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template__deferred_revenue_category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_account_followup_definition_form
msgid "Define follow-up levels and their related actions"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__method__degressive
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__method__degressive
msgid "Degressive"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_progress_factor
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_progress_factor
msgid "Degressive Factor"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Depreciation"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__depreciation_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__depreciation_date
msgid "Depreciation Date"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__move_id
msgid "Depreciation Entry"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__depreciation_line_ids
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Depreciation Method"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__name
msgid "Depreciation Name"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/asset_modify.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Depreciation line posted."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__description
#, python-format
msgid "Description"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_form_view
msgid "Description..."
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.financial_report_wiz_modified
msgid "Discard"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__display_account
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__display_account
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__display_account
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__display_account
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__display_account
msgid "Display Accounts"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__debit_credit
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__debit_credit
msgid "Display Debit/Credit Columns"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_account__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_journal__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_reconciliation_widget__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_day_book_report_template__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_agedpartnerbalance__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_bank_book__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_book__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_flow__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_financial__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_general_ledger__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_journal_audit__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_partnerledger__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_tax__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_trial_balance__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__display_detail__detail_flat
msgid "Display children flat"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__display_detail__detail_with_hierarchy
msgid "Display children with hierarchy"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__display_detail
msgid "Display details"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Document closed."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__state__draft
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__state__draft
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__asset_asset_report__state__draft
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Draft"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Due Date"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__delay
msgid "Due Days"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__effective_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__effective_date
msgid "Effective Date"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_payment__effective_date
#: model:ir.model.fields,help:base_accounting_kit.field_account_payment_register__effective_date
msgid "Effective date of PDC"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid "Either pass both debit and credit or none."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__enable_filter
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__enable_filter
msgid "Enable Comparison"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.es_config_settings_view_form_base_accounting_kit
msgid "Enable credit limit for customers"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__date_to
msgid "End Date"
msgstr "Date de fin"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_end
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__method_time__end
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__method_time__end
msgid "Ending Date"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_end
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__method_end
msgid "Ending date"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr "Écritures triées par"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "Entry Label"
msgstr ""

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_expense0
msgid "Expense"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "External link"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__filter_cmp
msgid "Filter by"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_financial
msgid "Financial Report"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__style_overwrite
msgid "Financial Report Style"
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_financial_report_tree
#: model:ir.model,name:base_accounting_kit.model_financial_report
msgid "Financial Reports"
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.report,name:base_accounting_kit.financial_report_pdf
msgid "Financial reports"
msgstr ""

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_financing_activity1
msgid "Financing Activities"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__followup_id
msgid "Follow Ups"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__name
msgid "Follow-Up Action"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__followup_line_ids
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_tree
msgid "Follow-up"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_followup_line
msgid "Follow-up Criteria"
msgstr ""

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_followup_menu
msgid "Follow-up Levels"
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_view_list_customer_statements
#: model:ir.ui.menu,name:base_accounting_kit.customer_statements_menu
msgid "Follow-up Reports"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_follower_ids
msgid "Followers"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__followup_status
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__followup_status
msgid "Followup status"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_financial_report__sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_account_followup_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"                possible to use print and e-mail templates to send specific messages to\n"
"                the customer."
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/report/account_bank_book.py:0
#: code:addons/base_accounting_kit/report/account_cash_book.py:0
#: code:addons/base_accounting_kit/report/account_day_book.py:0
#: code:addons/base_accounting_kit/report/cash_flow_report.py:0
#: code:addons/base_accounting_kit/report/general_ledger_report.py:0
#: code:addons/base_accounting_kit/report/report_aged_partner.py:0
#: code:addons/base_accounting_kit/report/report_journal_audit.py:0
#: code:addons/base_accounting_kit/report/report_partner_ledger.py:0
#: code:addons/base_accounting_kit/report/report_tax.py:0
#: code:addons/base_accounting_kit/report/report_trial_balance.py:0
#: code:addons/language_translation/base_accounting_kit/report/account_bank_book.py:0
#: code:addons/language_translation/base_accounting_kit/report/account_cash_book.py:0
#: code:addons/language_translation/base_accounting_kit/report/account_day_book.py:0
#: code:addons/language_translation/base_accounting_kit/report/cash_flow_report.py:0
#: code:addons/language_translation/base_accounting_kit/report/general_ledger_report.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_aged_partner.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_journal_audit.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_partner_ledger.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_tax.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_trial_balance.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__view_format
msgid "Format"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_general_ledger_menu
#: model:ir.actions.report,name:base_accounting_kit.action_report_general_ledger
#: model:ir.ui.menu,name:base_accounting_kit.menu_general_ledger
msgid "General Ledger"
msgstr "Grand livre général"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_report_general_ledger
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_general_ledger
msgid "General Ledger Report"
msgstr "État du grand livre"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr "Générer des entrées d'actifs"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "Générer des entrées"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__journal_state
msgid "Generate Journal As"
msgstr "Générer le journal en tant que"

#. module: base_accounting_kit
#: model:ir.actions.server,name:base_accounting_kit.recurring_template_cron_ir_actions_server
#: model:ir.cron,cron_name:base_accounting_kit.recurring_template_cron
#: model:ir.cron,name:base_accounting_kit.recurring_template_cron
msgid "Generate Recurring Entries"
msgstr "Générer des entrées récurrentes"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_reports_generic_statements
msgid "Generic Statements"
msgstr "Déclarations génériques"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_followup_line__sequence
msgid "Gives the sequence order when displaying a list of follow-up lines."
msgstr "Donne l'ordre de séquence lors de l'affichage d'une liste de lignes de suivi."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr "Aller aux relevés bancaires"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Good Job!"
msgstr "Bon travail!"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__gross_value
msgid "Gross Amount"
msgstr "Montant brut"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__value
msgid "Gross Value"
msgstr "Valeur brute"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr "Valeur brute de l'actif"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Group By"
msgstr "Par groupe"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Group By..."
msgstr "Par groupe..."

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__group_entries
msgid "Group Journal Entries"
msgstr "Entrées de journal de groupe"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__has_due
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__has_due
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__has_due
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__has_due
msgid "Has Due"
msgstr "A dû"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__financial_report__view_format__horizontal
msgid "Horizontal"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_account__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_journal__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_reconciliation_widget__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_day_book_report_template__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_agedpartnerbalance__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_bank_book__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_book__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_flow__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_financial__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_general_ledger__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_journal_audit__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_partnerledger__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_tax__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_trial_balance__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_config_settings__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__id
msgid "ID"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "INVOICES"
msgstr "FACTURES"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_needaction
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_has_error
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_bank_book_report__initial_balance
#: model:ir.model.fields,help:base_accounting_kit.field_account_cash_book_report__initial_balance
#: model:ir.model.fields,help:base_accounting_kit.field_account_report_general_ledger__initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__res_partner__followup_status__in_need_of_action
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_search_view
msgid "In need of action"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_payment.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_payment.py:0
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__initial_balance
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__initial_balance
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__initial_balance
msgid "Include Initial Balances"
msgstr ""

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_income0
msgid "Income"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Income/Expense"
msgstr "Revenus / dépenses"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first January / Start date of fiscal "
"year"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_investing_activity0
msgid "Investing Activities"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__invoice_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#, python-format
msgid "Invoice"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__invoice_list
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__invoice_list
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_form_view
msgid "Invoice Details"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__is_warning
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__is_warning
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__is_warning
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__is_warning
msgid "Is Warning"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_report_partner_ledger__amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__5
msgid "Italic Text (smaller)"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.payment_matching_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Items"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
msgid "JRNL"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model,name:base_accounting_kit.model_account_journal
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__journal_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__journal_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#, python-format
msgid "Journal"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__sortby__sort_journal_partner
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__sortby__sort_journal_partner
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__sortby__sort_journal_partner
msgid "Journal & Partner"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_print_journal__sort_selection__move_name
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Journal Entry Number"
msgstr "N° écriture dans le journal"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid "Journal Items"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_matching.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_matching.js:0
#, python-format
msgid "Journal Items to Reconcile"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_journal_audit
msgid "Journal Report"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "Journal and Partner"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__journal_ids
msgid "Journals"
msgstr "Journaux"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_print_journal_menu
#: model:ir.actions.report,name:base_accounting_kit.action_report_journal
#: model:ir.ui.menu,name:base_accounting_kit.menu_print_journal
msgid "Journals Audit"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#, python-format
msgid "Label"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_account____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_journal____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_reconciliation_widget____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_day_book_report_template____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_agedpartnerbalance____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_bank_book____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_book____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_flow____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_financial____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_general_ledger____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_journal_audit____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_partnerledger____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_tax____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_trial_balance____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Last Month"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Last Year"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__level
msgid "Level"
msgstr ""

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_liability0
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_liabilitysum0
msgid "Liability"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__method__linear
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__method__linear
msgid "Linear"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__move_check
msgid "Linked"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Load more"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Load more... ("
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__fiscalyear_lock_date
msgid "Lock Date"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr ""

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.menu_lock_dates
msgid "Lock Dates"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_lock_date
msgid "Lock date for accounting"
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.account_update_lock_date_act_window
msgid "Lock your Fiscal Period"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__1
msgid "Main Title 1 (bold, underlined)"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Manual Operations"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_ids
msgid "Messages"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
msgid "Modify"
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_asset_modify
#: model:ir.model,name:base_accounting_kit.model_asset_modify
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
msgid "Modify Asset"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Modify models"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Month"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__asset_mrr
msgid "Monthly Recurring Revenue"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__recurring_period__months
msgid "Months"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Move"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__template_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__name
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Name"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "Net"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Net Profit or Loss"
msgstr "Bénéfice ou perte net"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "New"
msgstr "Nouvelle"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__remaining_value
msgid "Next Period Depreciation"
msgstr "Amortissement de la période suivante"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__next_reminder_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__next_reminder_date
msgid "Next Reminder Date"
msgstr "Date du prochain rappel"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__next_date
msgid "Next Schedule"
msgstr "Calendrier suivant"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__cash_flow_report__filter_cmp__filter_no
msgid "No Filters"
msgstr "Aucun filtre"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__res_partner__followup_status__no_action_needed
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_search_view
msgid "No action needed"
msgstr "Pas d'action requise"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__display_detail__no_detail
msgid "No detail"
msgstr "Aucun détail"

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr "Pas de suivi à envoyer!"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_lock_date__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__4
msgid "Normal Text"
msgstr "Texte normal"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Not archived"
msgstr "Non archivé"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__note
#, python-format
msgid "Note"
msgstr "Remarque"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Nothing to do!"
msgstr "Rien à faire!"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_number
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_number
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__method_number
msgid "Number of Depreciations"
msgstr "Nombre d'amortissements"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__method_time__number
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__method_time__number
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Number of Entries"
msgstr "Nombre d'entrées"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_period
msgid "Number of Months in a Period"
msgstr "Nombre de mois dans une période"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de messages nécessitant une action"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec erreur de livraison"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "One Entry Every"
msgstr "Une entrée chaque"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_lock_date__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_model.js:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_model.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Open balance"
msgstr "Solde ouvert"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_operation0
msgid "Operations"
msgstr "Opérations"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_form_view
msgid "Other Info"
msgstr "Autre info"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_search_view
msgid "Overdue Invoices"
msgstr "Factures en retard"

#. module: base_accounting_kit
#: model:account.payment.method,name:base_accounting_kit.account_payment_method_pdc_in
#: model:account.payment.method,name:base_accounting_kit.account_payment_method_pdc_out
msgid "PDC"
msgstr "PDC"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#, python-format
msgid "Paid"
msgstr "Payé"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__parent_id
msgid "Parent"
msgstr "Parente"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_search
msgid "Parent Report"
msgstr "Rapport des parents"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__partner_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__partner_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__partner_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#, python-format
msgid "Partner"
msgstr "Partenaire"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_partner_leadger
#: model:ir.actions.report,name:base_accounting_kit.action_report_partnerledger
#: model:ir.ui.menu,name:base_accounting_kit.menu_partner_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
msgid "Partner Ledger"
msgstr "Grand livre des partenaires"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_partnerledger
msgid "Partner Ledger Report"
msgstr "Rapport du grand livre des partenaires"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_reports_partner
msgid "Partner Reports"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__result_selection
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__result_selection
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__result_selection
msgid "Partner's"
msgstr "Les partenaires"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Partners"
msgstr "Les partenaires"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__pay_time__pay_now
msgid "Pay Directly"
msgstr "Payer directement"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__pay_time__pay_later
msgid "Pay Later"
msgstr "Payer plus tard"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__pay_time
msgid "Pay Time"
msgstr "Temps de paie"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Pay your"
msgstr "Payez votre"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_aged_trial_balance__result_selection__supplier
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_partner_report__result_selection__supplier
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_partner_ledger__result_selection__supplier
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Payable Accounts"
msgstr "Comptes fournisseurs"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_followup_definition_form
msgid "Payment Follow-ups"
msgstr "Suivi des paiements"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.matching_account_payment
msgid "Payment Matching"
msgstr "Rapprochement des paiements"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_payment
msgid "Payments"
msgstr "Paiements"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.payment_matching_view
msgid "Payments Matching"
msgstr "Rapprochement des paiements"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_payment.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_payment.py:0
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' or 'PDC' selected as payment"
" method and not have already been reconciled"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_payment.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr "Les paiements sans client ne peuvent pas être jumelés"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_period
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__method_period
msgid "Period Length"
msgstr "Durée de la période"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__period_length
msgid "Period Length (days)"
msgstr "Durée de la période (jours)"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Periodicity"
msgstr "Périodicité"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr "Enregistrer les lignes d'amortissement"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_asset.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_asset.js:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__move_posted_check
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__move_check
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__journal_state__posted
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "Publié"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__posted_value
msgid "Posted Amount"
msgstr "Montant publié"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "Lignes d'amortissement publiées"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__sign__1
msgid "Preserve balance sign"
msgstr "Posté dépréciation"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Presets config"
msgstr "Configuration des préréglages"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_bank_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_cash_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_day_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.financial_report_wiz_modified
msgid "Print"
msgstr "Impression"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_payment.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_payment.py:0
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "Imprimer des chèques pré-numérotés"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"Imprimer le rapport avec la colonne monnaie si la devise diffère de la "
"devise de la société."

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_product_template
msgid "Product Template"
msgstr "Modèle d'article"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "Bénéfice (perte) à déclarer"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:base_accounting_kit.action_profit_and_loss_report
#: model:ir.ui.menu,name:base_accounting_kit.account_financial_reports_profit_loss
msgid "Profit and Loss"
msgstr "Profit et perte"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__prorata
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__prorata
msgid "Prorata Temporis"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid ""
"Prorata temporis can be applied only for time method \"number of "
"depreciations\"."
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Purchase"
msgstr "achat"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Purchase Month"
msgstr "Mois d'achat"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__type__purchase
msgid "Purchase: Asset"
msgstr "Achat: actif"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__name
msgid "Reason"
msgstr "Raison"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_aged_trial_balance__result_selection__customer
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_partner_report__result_selection__customer
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_partner_ledger__result_selection__customer
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Receivable Accounts"
msgstr "Comptes clients"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_aged_trial_balance__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_partner_report__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_partner_ledger__result_selection__customer_supplier
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "Comptes débiteurs et créditeurs"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Recognition Account"
msgstr "Compte de reconnaissance"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr "Compte de revenu de reconnaissance"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.payment_matching_view
#, python-format
msgid "Reconcile"
msgstr "Réconcilier"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__reconciled
msgid "Reconciled Entries"
msgstr "Entrées rapprochées"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__recurring_interval
msgid "Recurring Interval"
msgstr "Intervalle récurrent"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__recurring_lines
msgid "Recurring Lines"
msgstr "Lignes récurrentes"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__recurring_period
msgid "Recurring Period"
msgstr "Période récurrente"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__recurring_ref
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__recurring_ref
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__recurring_ref
msgid "Recurring Ref"
msgstr "Réf récurrent"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_form_view
msgid "Recurring Template"
msgstr "Modèle récurrent"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_recurring_payments_view
#: model:ir.ui.menu,name:base_accounting_kit.account_recurring_payments_child1
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_tree_view
msgid "Recurring Templates"
msgstr "Modèles récurrents"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#, python-format
msgid "Ref"
msgstr "Réf"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__code
msgid "Reference"
msgstr "Référence"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_payment_register
msgid "Register Payment"
msgstr "Enregistrer un paiement"

#. module: base_accounting_kit
#: model:followup.line,name:base_accounting_kit.followup_line_id
msgid "Reminder"
msgstr "Rappel"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_form
msgid "Report"
msgstr "rapport"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__name
msgid "Report Name"
msgstr "Nom du rapport"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_aged_balance_view
msgid "Report Options"
msgstr "Options de rapport"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_search
msgid "Report Type"
msgstr "Type de rapport"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__account_report_id
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__type__account_report
msgid "Report Value"
msgstr "Valeur du rapport"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#, python-format
msgid "Residual"
msgstr "Résiduel"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__value_residual
msgid "Residual Value"
msgstr "Valeur résiduelle"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__sign__-1
msgid "Reverse balance sign"
msgstr "Signe d'équilibre inversé"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__state__open
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__state__running
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__asset_asset_report__state__open
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Running"
msgstr "Fonctionnement"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur de livraison SMS"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "Sale"
msgstr "Vente"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__type__sale
msgid "Sale: Revenue Recognition"
msgstr "Vente: reconnaissance des revenus"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Sales"
msgstr "Ventes"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_sale_order
msgid "Sales Order"
msgstr "Bon de commande"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__salvage_value
msgid "Salvage Value"
msgstr "Valeur de récupération"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Save and New"
msgstr "Enregistrer et nouveau"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "Rechercher une catégorie d'actif"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_filter
msgid "Search Follow-up"
msgstr "Suivi de la recherche"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#, python-format
msgid "Select Partner"
msgstr "Sélectionnez un partenaire"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Sélectionnez un partenaire ou choisissez un homologue"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr "Vendre ou éliminer"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__sequence
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__sequence
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "Définir sur brouillon"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Settings"
msgstr "Paramètres"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__sign
msgid "Sign on Reports"
msgstr "Signer sur les rapports"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Skill Level: 50%"
msgstr "Niveau de compétence: 50%"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Skip"
msgstr "Sauter"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__6
msgid "Smallest Text"
msgstr "Le plus petit texte"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#, python-format
msgid "Some fields are undefined"
msgstr "Certains champs ne sont pas définis"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__sortby
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__sortby
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__sortby
msgid "Sort by"
msgstr "Trier par"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__date_from
msgid "Start Date"
msgstr "Date de début"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__date
msgid "Starting Date"
msgstr "Date de début"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "Indiquez ici le temps entre 2 amortissements, en mois"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__parent_state
msgid "State of Asset"
msgstr "État de l'actif"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__state
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__state
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__state
msgid "Status"
msgstr "Statut"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Supplier Invoice"
msgstr "Facture fournisseur"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "TOP 10 CUSTOMERS"
msgstr "TOP 10 CLIENTS"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__target_move
msgid "Target Moves"
msgstr "Mouvements cibles"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "Tax"
msgstr "Impôt"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Tax Amount"
msgstr "Montant de la taxe"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Tax Declaration"
msgstr "Déclaration d'impôts"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr "Taxe incluse dans le prix"

#. module: base_accounting_kit
#: model:ir.actions.report,name:base_accounting_kit.action_report_account_tax
#: model:ir.model,name:base_accounting_kit.model_kit_account_tax_report
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_tax
#: model:ir.ui.menu,name:base_accounting_kit.menu_tax_report
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "Tax Report"
msgstr "Rapport fiscal"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_tax_report
msgid "Tax Reports"
msgstr "Rapports fiscaux"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Taxes"
msgstr "Les impôts"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "That's on average"
msgstr "C'est en moyenne"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_model.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr "Le montant %s n'est pas un montant partiel valide"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__method_period
msgid "The amount of time between two depreciations, in months"
msgstr "Le temps entre deux amortissements, en mois"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder.  Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__method_number
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "Le nombre d'amortissements nécessaires pour amortir votre bien"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_move.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_move.py:0
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be null."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "Il n'y a rien à réconcilier."

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_move_form_inherited
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.header_view
msgid "This Customer's due amount is"
msgstr "Le montant dû par ce client est"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "This Month"
msgstr "Ce mois-ci"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "This Year"
msgstr "Cette année"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_move_form_inherited
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.header_view
msgid "This customer's <strong>warning limit</strong> has been crossed."
msgstr "Ce client <strong>limite d'avertissement</strong> a été traversé."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid ""
"This depreciation is already linked to a journal entry! Please post or "
"delete it."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_cash_flow_report__label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_cash_flow_report__debit_credit
#: model:ir.model.fields,help:base_accounting_kit.field_financial_report__debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Ce paiement est enregistré mais non réconcilié."

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period of asset/revenue recognition as well."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_time
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_time
msgid "Time Method"
msgstr "Méthode de temps"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "Méthode de temps basée sur"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__2
msgid "Title 2 (bold)"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__3
msgid "Title 3 (bold, smaller)"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "To Check"
msgstr "Vérifier"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_form
msgid ""
"To remind customers of paying their invoices, you can\n"
"                        define different actions depending on how severely\n"
"                        overdue the customer is. These actions are bundled\n"
"                        into follow-up levels that are triggered when the due\n"
"                        date of an invoice has passed a certain\n"
"                        number of days. If there are other overdue invoices for the\n"
"                        same customer, the actions of the most\n"
"                        overdue invoice will be executed."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr "Pour accélérer la réconciliation, définissez"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_tree_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Total"
msgstr "Total"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__total_due
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__total_due
msgid "Total Due"
msgstr "Total dû"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Total Expenses"
msgstr "Dépenses totales"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Total Income"
msgstr "Revenu total"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__total_overdue
msgid "Total Overdue"
msgstr "Total en retard"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__due_amount
msgid "Total Sale"
msgstr "Vente totale"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Transaction"
msgstr "Transaction"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_balance_menu
#: model:ir.actions.report,name:base_accounting_kit.action_report_trial_balance
#: model:ir.ui.menu,name:base_accounting_kit.menu_Balance_report
msgid "Trial Balance"
msgstr "Balance de vérification"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_balance_report
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_trial_balance
msgid "Trial Balance Report"
msgstr "Rapport de balance de vérification"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__type
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__type
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__type
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Type"
msgstr "Type"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/report/report_aged_partner.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_aged_partner.py:0
#, python-format
msgid "Unknown Partner"
msgstr "Partenaire inconnu"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_asset.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_asset.js:0
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__journal_state__draft
#, python-format
msgid "Unposted"
msgstr "Non publié"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__unposted_value
msgid "Unposted Amount"
msgstr "Montant non comptabilisé"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_unread
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Compteur de messages non lus"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#, python-format
msgid "Unreconciled"
msgstr "Non réconcilié"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Unreconciled items"
msgstr "Éléments non rapprochés"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_update_lock_date_form_view
msgid "Update"
msgstr "Mise à jour"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Validate"
msgstr "Valider"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "Vendeur"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Verify"
msgstr "Vérifier"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__financial_report__view_format__vertical
msgid "Vertical"
msgstr "Vertical"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__type__sum
msgid "View"
msgstr "Vue"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__warning_stage
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__warning_stage
msgid "Warning Amount"
msgstr "Montant de l'avertissement"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/credit_limit.py:0
#: code:addons/language_translation/base_accounting_kit/models/credit_limit.py:0
#, python-format
msgid "Warning amount should be less than Blocking amount"
msgstr "Le montant de l'avertissement doit être inférieur au montant de blocage"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__website_message_ids
msgid "Website Messages"
msgstr "Messages du site Web"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__website_message_ids
msgid "Website communication history"
msgstr "Historique des communications sur le site Web"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__recurring_period__weeks
msgid "Weeks"
msgstr "Semaines"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__state
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_depreciation_line__parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__amount_currency
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__amount_currency
msgid "With Currency"
msgstr "Avec devise"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_balance_report__display_account__not_zero
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__display_account__not_zero
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__display_account__not_zero
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_account_report__display_account__not_zero
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__display_account__not_zero
msgid "With balance is not equal to 0"
msgstr "Avec solde n'est pas égal à 0"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "With balance not equal to zero"
msgstr "Avec un solde différent de zéro"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_balance_report__display_account__movement
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__display_account__movement
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__display_account__movement
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_account_report__display_account__movement
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__display_account__movement
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "With movements"
msgstr "Avec des mouvements"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "Avec factures en retard"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid "Write-Off"
msgstr "Écrire"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Writeoff Date"
msgstr "Annulation de la date"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__name
msgid "Year"
msgstr "An"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__recurring_period__years
msgid "Years"
msgstr "Années"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/account_lock_date.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/account_lock_date.py:0
#, python-format
msgid "You are not allowed to execute this action."
msgstr "Vous n'êtes pas autorisé à exécuter cette action."

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_financial_report__style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document is in %s state."
msgstr "Vous ne pouvez pas supprimer un document est dans %s Etat."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr "Vous ne pouvez pas supprimer un document contenant des entrées validées."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr "Vous ne pouvez pas supprimer les lignes d'amortissement validées."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted installment lines."
msgstr "Vous ne pouvez pas supprimer les lignes de versement validées."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/account_bank_book_wizard.py:0
#: code:addons/base_accounting_kit/wizard/account_cash_book_wizard.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/account_bank_book_wizard.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/account_cash_book_wizard.py:0
#, python-format
msgid "You must choose a Start Date"
msgstr "Vous devez choisir une date de début"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/general_ledger.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/general_ledger.py:0
#, python-format
msgid "You must define a Start Date"
msgstr "Vous devez définir une date de début"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/aged_partner.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/aged_partner.py:0
#, python-format
msgid "You must set a period length greater than 0."
msgstr "Vous devez définir une durée de période supérieure à 0.Vous devez définir une date de début."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/aged_partner.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/aged_partner.py:0
#, python-format
msgid "You must set a start date."
msgstr "Vous devez définir une date de début."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "You reconciled"
msgstr "Tu t'es réconcilié."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "and follow-up customers"
msgstr "et suivi des clients"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "e.g. Computers"
msgstr "par exemple. Des ordinateurs"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr "par exemple. Ordinateur portable iBook"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_form_view
msgid "has no due amount."
msgstr "n'a pas de montant dû"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr "ont été réconciliés automatiquement"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__tmpl_id
msgid "id"
msgstr "id"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "months"
msgstr "mois"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "reconcile"
msgstr "réconcilier"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "reconciliation models"
msgstr "modèles de réconciliation"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "remaining)"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_form_view
msgid "report"
msgstr "rapport"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "secondes par transaction."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "statement lines"
msgstr "lignes de relevé"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "transactions in"
msgstr "transactions dans"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "factures impayées"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "unreconciled entries"
msgstr "entrées non rapprochées"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "vendor bills"
msgstr "factures du fournisseur"
