# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_accounting_kit
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-04 10:29+0000\n"
"PO-Revision-Date: 2021-02-04 10:29+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid " (grouped)"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__entry_count
msgid "# Asset Entries"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__depreciation_nbr
msgid "# of Depreciation Lines"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__installment_nbr
msgid "# of Installment Lines"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/credit_limit.py:0
#: code:addons/base_accounting_kit/models/credit_limit.py:0
#: code:addons/language_translation/base_accounting_kit/models/credit_limit.py:0
#: code:addons/language_translation/base_accounting_kit/models/credit_limit.py:0
#, python-format
msgid "%s is in  Blocking Stage and has a due amount of %s %s to pay"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
msgid ": Bank Book Report"
msgstr ": تقرير كتاب البنك"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
msgid ": Cash Book Report"
msgstr ": تقرير دفتر النقدية"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
msgid ": Day Book Report"
msgstr ": تقرير كتاب اليوم"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid ": General ledger"
msgstr ": دفتر الأستاذ العام"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid ": Trial Balance"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid ""
"<br/>\n"
"                                    <strong>Date to :</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_kanban
msgid "<i class=\"fa fa-building\" role=\"img\" aria-label=\"Enterprise\"/>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
msgid "<span>Comp</span>"
msgstr "<span> شركات </ span>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span> غير مستحقة </ span>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid "<strong>Balance</strong>"
msgstr "<strong> الرصيد </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "<strong>Company:</strong>"
msgstr "<strong> الشركة: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid "<strong>Credit</strong>"
msgstr "<strong> الائتمان </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "<strong>Date from :</strong>"
msgstr "<strong> التاريخ من: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "<strong>Date to :</strong>"
msgstr "<strong> التاريخ إلى: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid "<strong>Debit</strong>"
msgstr "<strong> الخصم </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "<strong>Display Account:</strong>"
msgstr "<strong> حساب العرض: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "<strong>Display Account</strong>"
msgstr "<strong> حساب العرض </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong> الإدخالات مرتبة حسب: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "<strong>Journal:</strong>"
msgstr "<strong> المجلة: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "<strong>Journals:</strong>"
msgstr "<strong> المجلات: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
msgid "<strong>Name</strong>"
msgstr "<strong> الاسم </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr "<strong> الشريك: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "<strong>Purchase</strong>"
msgstr "<strong> شراء </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "<strong>Sorted By:</strong>"
msgstr "<strong> مرتبة حسب: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr "<strong> تاريخ البدء: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "<strong>Target Moves:</strong>"
msgstr "<strong> حركات الهدف: </ strong>"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "<strong>Total</strong>"
msgstr "<strong> الإجمالي </ strong>"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "يجب أن تتضمن التسوية خطي نقل على الأقل."

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_res_partner__warning_stage
#: model:ir.model.fields,help:base_accounting_kit.field_res_users__warning_stage
msgid ""
"A warning message will appear once the selected customer is crossed warning "
"amount. Set its value to 0.00 to disable this feature"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model,name:base_accounting_kit.model_account_account
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
#, python-format
msgid "Account"
msgstr "حساب"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr "تقرير ميزان المراجعة المسن في الحساب"

#. module: base_accounting_kit
#: model:ir.actions.server,name:base_accounting_kit.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:base_accounting_kit.account_asset_cron
#: model:ir.cron,name:base_accounting_kit.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr "أصل الحساب: إنشاء إدخالات الأصول"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_bank_book_report
msgid "Account Bank Book Report"
msgstr "تقرير دفتر بنك الحساب"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_cash_book_report
msgid "Account Cash Book Report"
msgstr "تقرير دفتر النقدية للحساب"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_common_account_report
msgid "Account Common Account Report"
msgstr "تقرير الحساب المشترك"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr "تقرير الشريك المشترك للحساب"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__date
msgid "Account Date"
msgstr "تاريخ الحساب"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_day_book_report
msgid "Account Day Book Report"
msgstr "تقرير دفتر يوم الحساب"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_followup
msgid "Account Follow-up"
msgstr "متابعة الحساب"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr "حساب شريك الأستاذ"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_print_journal
msgid "Account Print Journal"
msgstr "طباعة دفتر اليومية"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "أداة تسوية الحساب"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_recurring_entries_line
msgid "Account Recurring Entries Line"
msgstr "سطر إدخالات الحساب المتكررة"

#. module: base_accounting_kit
#: model:ir.actions.client,name:base_accounting_kit.action_account_invoice_report_all
#: model:ir.model,name:base_accounting_kit.model_account_financial_report
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__children_ids
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_tree
msgid "Account Report"
msgstr "تقرير الحساب"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__account_report_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__account_report_id
#: model:ir.ui.menu,name:base_accounting_kit.menu_account_financial_reports_tree
msgid "Account Reports"
msgstr "تقارير الحساب"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Account Total"
msgstr "إجمالي الحساب"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__type__account_type
msgid "Account Type"
msgstr "نوع الحساب"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__account_type_ids
msgid "Account Types"
msgstr "أنواع الحسابات"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.res_config_settings_view_accounting_kit
msgid "Accounting"
msgstr "محاسبة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_form_view
msgid "Accounting Info"
msgstr "معلومات المحاسبة"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_recurring_payments
msgid "Accounting Recurring Payment"
msgstr "المدفوعات المحاسبية المتكررة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_asset.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_asset.js:0
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr "إدخالات المحاسبة في انتظار التحقق اليدوي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__account_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__account_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__account_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__account_ids
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__type__accounts
msgid "Accounts"
msgstr "حسابات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_needaction
msgid "Action Needed"
msgstr "الإجراءات اللازمة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__active
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__active
msgid "Active"
msgstr "نشيط"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__active_limit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__active_limit
msgid "Active Credit Limit"
msgstr "حد الائتمان النشط"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Additional Options"
msgstr "خيارات اضافية"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_line_form
msgid "After"
msgstr "بعد"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_aged_balance_view
#: model:ir.actions.report,name:base_accounting_kit.action_report_aged_partner_balance
#: model:ir.ui.menu,name:base_accounting_kit.menu_aged_trial_balance
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "رصيد الشريك المسن"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_agedpartnerbalance
msgid "Aged Partner Balance Report"
msgstr "تقرير رصيد الشريك المسن"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Aged Payable"
msgstr "مسن دائن"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Aged Receivable"
msgstr "مستحق قديم"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_balance_report__display_account__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__display_account__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__display_account__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_account_report__display_account__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__display_account__all
msgid "All"
msgstr "الكل"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__target_move__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__target_move__all
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_day_book_report__target_move__all
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "All Entries"
msgstr "كل المقالات"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__target_move__posted
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__target_move__posted
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_day_book_report__target_move__posted
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_flow
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "All Posted Entries"
msgstr "جميع المشاركات المنشورة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "All accounts"
msgstr "جميع الحسابات"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "All accounts'"
msgstr "جميع الحسابات"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__amount
#, python-format
msgid "Amount"
msgstr "كمية"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "مقدار خطوط الاستهلاك"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__installment_value
msgid "Amount of Installment Lines"
msgstr "مقدار خطوط التقسيط"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Analytic Acc."
msgstr "Acc التحليلي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__account_analytic_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__analytic_account_id
msgid "Analytic Account"
msgstr "حساب تحليلي"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Analytic Tags."
msgstr "العلامات التحليلية."

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__asset_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__asset_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Asset"
msgstr "الأصل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__account_asset_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Asset Account"
msgstr "حساب الأصول"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__asset_category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_invoice_asset_category
msgid "Asset Category"
msgstr "فئة الأصول"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "مدد الأصول المراد تعديلها"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__asset_end_date
msgid "Asset End Date"
msgstr "تاريخ انتهاء الأصل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__asset_method_time
msgid "Asset Method Time"
msgstr "وقت أسلوب الأصول"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__name
msgid "Asset Name"
msgstr "اسم الأصل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__asset_start_date
msgid "Asset Start Date"
msgstr "تاريخ بدء الأصل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__name
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_product__asset_category_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template__asset_category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Asset Type"
msgstr "نوع الأصول"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:base_accounting_kit.menu_action_account_asset_asset_list_normal_purchase
msgid "Asset Types"
msgstr "أنواع الأصول"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_asset_category
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__asset_category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_tree
msgid "Asset category"
msgstr "فئة الأصول"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr "تم إنشاء الأصل"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr "خط إهلاك الأصول"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr "بيع الأصول أو التخلص منها. قيد المحاسبة في انتظار التحقق من صحته."

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr "الاعتراف بالأصول / الإيرادات"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_assets0
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_asset_asset_form
#: model:ir.ui.menu,name:base_accounting_kit.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:base_accounting_kit.menu_action_asset_asset_report
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Assets"
msgstr "الأصول"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_asset_asset_report
#: model:ir.model,name:base_accounting_kit.model_asset_asset_report
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.action_account_asset_report_graph
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.action_account_asset_report_pivot
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "تحليل الأصول"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__asset_depreciation_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__asset_depreciation_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr "خطوط إهلاك الأصول"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "الأصول والإيرادات"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Assets in closed state"
msgstr "الأصول في حالة مغلقة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "الأصول في حالات السحب والمفتوحة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "الأصول في حالة المسودة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "الأصول في حالة التشغيل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_reports_audit
msgid "Audit Reports"
msgstr "تقارير التدقيق"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__open_asset
msgid "Auto-confirm Assets"
msgstr "تأكيد الأصول تلقائيًا"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__0
msgid "Automatic formatting"
msgstr "تنسيق تلقائي"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "BANK AND CASH BALANCE"
msgstr "الرصيد المصرفي والنقدي"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "Balance"
msgstr "توازن"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:base_accounting_kit.action_balance_sheet_report
#: model:ir.ui.menu,name:base_accounting_kit._account_financial_reports_balance_sheet
msgid "Balance Sheet"
msgstr "ورقة التوازن"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_bank_book_menu
msgid "Bank Book"
msgstr "كتاب البنك"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_bank_book_view
#: model:ir.actions.report,name:base_accounting_kit.action_report_bank_book
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_bank_book_form_view
msgid "Bank Book Report"
msgstr "تقرير دفتر البنك"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_matching.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_matching.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr "التسويات المصرفية"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__bank_reference
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__bank_reference
msgid "Bank Reference"
msgstr "مرجع البنك"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Base Amount"
msgstr "كمية أساسية"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__blocking_stage
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__blocking_stage
msgid "Blocking Amount"
msgstr "مبلغ المنع"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_bank_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_cash_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_day_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_update_lock_date_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "إلغاء"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_res_partner__blocking_stage
#: model:ir.model.fields,help:base_accounting_kit.field_res_users__blocking_stage
msgid ""
"Cannot make sales once the selected customer is crossed blocking amount.Set "
"its value to 0.00 to disable this feature"
msgstr ""

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_cash_book_menu
msgid "Cash Book"
msgstr "دفتر النقدية"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_cash_book_view
#: model:ir.actions.report,name:base_accounting_kit.action_report_cash_book
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_cash_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_day_book_form_view
msgid "Cash Book Report"
msgstr "تقرير دفتر النقدية"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_cash_flow_report
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_cash_flow
msgid "Cash Flow Report"
msgstr "تقرير التدفق النقدي"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_cash_flow0
#: model:ir.actions.act_window,name:base_accounting_kit.action_cash_flow_report
#: model:ir.actions.report,name:base_accounting_kit.action_report_cash_flow
#: model:ir.ui.menu,name:base_accounting_kit.menu_account_cash_flow_report
msgid "Cash Flow Statement"
msgstr "بيان التدفقات النقدية"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_account__cash_flow_type
msgid "Cash Flow type"
msgstr "نوع التدفق النقدي"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.cash_in_financial0
#: model:account.financial.report,name:base_accounting_kit.cash_in_from_operation0
#: model:account.financial.report,name:base_accounting_kit.cash_in_investing0
msgid "Cash In"
msgstr "التدفقات النقدية الداخلة"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.cash_out_financial1
#: model:account.financial.report,name:base_accounting_kit.cash_out_investing1
#: model:account.financial.report,name:base_accounting_kit.cash_out_operation1
msgid "Cash Out"
msgstr "المصروفات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Category"
msgstr "الفئة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Category of asset"
msgstr "فئة الأصول"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Check all"
msgstr "تحقق من الكل"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr "تحقق من عدم وجود أسطر كشف حساب بنكي لـ"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr "حدد هذا الخيار إذا كنت تريد تجميع الإدخالات التي تم إنشاؤها حسب الفئات."

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__cheque_reference
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__cheque_reference
msgid "Cheque Reference"
msgstr "تحقق من المرجع"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "اختر نظيرًا أو أنشئ شطبًا"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__method
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__method_time
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_asset_depreciation_confirmation_wizard__date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_account_recurring_payments_view
msgid "Click to create new recurring payment template"
msgstr "انقر لإنشاء قالب دفع متكرر جديد"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__state__close
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__asset_asset_report__state__close
#, python-format
msgid "Close"
msgstr "قريب"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Close statement"
msgstr "بيان وثيق"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Closed"
msgstr "مغلق"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "Code"
msgstr "الشفرة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__label_filter
msgid "Column Label"
msgstr "تسمية العمود"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__company_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__company_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Company"
msgstr "شركة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__name
msgid "Company Name"
msgstr "اسم الشركة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.cash_flow_report_view
msgid "Comparison"
msgstr "مقارنة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method
msgid "Computation Method"
msgstr "طريقة الحساب"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr "حساب الأصول"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr "حساب الإهلاك"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الاعدادات"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Confirm"
msgstr "تؤكد"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "تهانينا ، لقد انتهيت من كل شيء!"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Create a counterpart"
msgstr "خلق نظير"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Create model"
msgstr "إنشاء نموذج"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/asset_depreciation_confirmation_wizard.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Asset Moves"
msgstr "عمليات نقل الأصول المُنشأة"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/asset_depreciation_confirmation_wizard.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Revenue Moves"
msgstr "تحركات الإيرادات المُنشأة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__create_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__create_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "Credit"
msgstr "ائتمان"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__credit_account
msgid "Credit Account"
msgstr "حساب الائتمان"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_customer_form
msgid "Credit Limit"
msgstr "الحد الائتماني"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__enable_credit_limit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__enable_credit_limit
msgid "Credit Limit Enabled"
msgstr "تمكين حد الائتمان"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__depreciated_value
msgid "Cumulative Depreciation"
msgstr "الاهلاك التراكمي"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__currency_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#, python-format
msgid "Currency"
msgstr "عملة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Current"
msgstr "تيار"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__amount
msgid "Current Depreciation"
msgstr "الإهلاك الحالي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_config_settings__customer_credit_limit
msgid "Customer Credit Limit"
msgstr "حد ائتمان العميل"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Customer Invoice"
msgstr "فاتورة العميل"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr "مطابقة العملاء / البائعين"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_reports_daily_reports
msgid "Daily Reports"
msgstr "التقارير اليومية"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: model:ir.ui.menu,name:base_accounting_kit.menu_accounting_dashboard
#, python-format
msgid "Dashboard"
msgstr "لوحة القيادة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__sortby__sort_date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__sortby__sort_date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_print_journal__sort_selection__date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__sortby__sort_date
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__cash_flow_report__filter_cmp__filter_date
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#, python-format
msgid "Date"
msgstr "التاريخ"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__date_to_cmp
msgid "Date End"
msgstr "تاريخ الانتهاء"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__date_from_cmp
msgid "Date Start"
msgstr "تاريخ البدء"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Date of asset"
msgstr "تاريخ الأصل"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr "تاريخ شراء الأصل"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr "تاريخ الاستهلاك"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.cash_flow_report_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.financial_report_wiz_modified
msgid "Dates"
msgstr "تواريخ"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_day_book_menu
msgid "Day Book"
msgstr "كتاب اليوم"

#. module: base_accounting_kit
#: model:ir.actions.report,name:base_accounting_kit.day_book_pdf_report
msgid "Day Book PDF Report"
msgstr "تقرير PDF للكتاب اليومي"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_day_book_view
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_day_book_report_template
msgid "Day Book Report"
msgstr "تقرير كتاب اليوم"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__recurring_period__days
msgid "Days"
msgstr "أيام"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "Debit"
msgstr "مدين"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__debit_account
msgid "Debit Account"
msgstr "حساب مدين"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr "حساب الإيرادات المؤجلة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_product__deferred_revenue_category_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template__deferred_revenue_category_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr "نوع الإيرادات المؤجلة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr "الإيرادات المؤجلة"

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_account_followup_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "تحديد مستويات المتابعة والإجراءات المتعلقة بها"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__method__degressive
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__method__degressive
msgid "Degressive"
msgstr "الانحدار"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_progress_factor
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_progress_factor
msgid "Degressive Factor"
msgstr "عامل الانحدار"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Depreciation"
msgstr "الاستهلاك"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "مجلس الإهلاك"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__depreciation_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__depreciation_date
msgid "Depreciation Date"
msgstr "تاريخ الإهلاك"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr "إدخالات الإهلاك: حساب الأصول"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr "إدخالات الإهلاك: حساب المصاريف"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__move_id
msgid "Depreciation Entry"
msgstr "إدخال الإهلاك"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr "معلومات الإهلاك"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__depreciation_line_ids
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr "خطوط الإهلاك"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "طريقة الإهلاك"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr "شهر الإهلاك"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__name
msgid "Depreciation Name"
msgstr "اسم الإهلاك"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/asset_modify.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr "تم تعديل لوحة الإهلاك"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Depreciation line posted."
msgstr "تم ترحيل بند الإهلاك."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__description
#, python-format
msgid "Description"
msgstr "وصف"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_form_view
msgid "Description..."
msgstr "وصف..."

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.financial_report_wiz_modified
msgid "Discard"
msgstr "تجاهل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__display_account
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__display_account
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__display_account
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__display_account
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__display_account
msgid "Display Accounts"
msgstr "عرض الحسابات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__debit_credit
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__debit_credit
msgid "Display Debit/Credit Columns"
msgstr "عرض أعمدة الخصم / الائتمان"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_account__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_journal__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_reconciliation_widget__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_day_book_report_template__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_agedpartnerbalance__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_bank_book__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_book__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_flow__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_financial__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_general_ledger__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_journal_audit__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_partnerledger__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_tax__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_trial_balance__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__display_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__display_detail__detail_flat
msgid "Display children flat"
msgstr "عرض الأطفال مسطح"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__display_detail__detail_with_hierarchy
msgid "Display children with hierarchy"
msgstr "عرض الأطفال مع التسلسل الهرمي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__display_detail
msgid "Display details"
msgstr "عرض التفاصيل"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr "نقل التخلص"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "Document closed."
msgstr "تحركات التخلص"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__state__draft
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__state__draft
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__asset_asset_report__state__draft
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Draft"
msgstr "مشروع"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Due Date"
msgstr "تاريخ الاستحقاق"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__delay
msgid "Due Days"
msgstr "أيام الاستحقاق"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__effective_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__effective_date
msgid "Effective Date"
msgstr "تاريخ النفاذ"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_payment__effective_date
#: model:ir.model.fields,help:base_accounting_kit.field_account_payment_register__effective_date
msgid "Effective date of PDC"
msgstr "تاريخ نفاذ PDC"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "إما أن تمرر كلاً من الخصم والائتمان أو لا شيء."

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__enable_filter
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__enable_filter
msgid "Enable Comparison"
msgstr "تمكين المقارنة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.es_config_settings_view_form_base_accounting_kit
msgid "Enable credit limit for customers"
msgstr "تمكين حد الائتمان للعملاء"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__date_to
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__date_to
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_end
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__method_time__end
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__method_time__end
msgid "Ending Date"
msgstr "تاريخ الانتهاء"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_end
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__method_end
msgid "Ending date"
msgstr "تاريخ الانتهاء"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr "قيود يوميه مصنفة حسب"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "Entry Label"
msgstr "بطاقة الدخول"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_expense0
msgid "Expense"
msgstr "مصروف"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "المرشحات الممتدة ..."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "External link"
msgstr "رابط خارجي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__filter_cmp
msgid "Filter by"
msgstr "مصنف بواسطة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr "تصفية حسب الحساب ، التسمية ، الشريك ، المبلغ ، ..."

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_financial
msgid "Financial Report"
msgstr "تقرير مالي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__style_overwrite
msgid "Financial Report Style"
msgstr "نمط التقرير المالي"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_financial_report_tree
#: model:ir.model,name:base_accounting_kit.model_financial_report
msgid "Financial Reports"
msgstr "تقارير مالية"

#. module: base_accounting_kit
#: model:ir.actions.report,name:base_accounting_kit.financial_report_pdf
msgid "Financial reports"
msgstr "تقارير مالية"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_financing_activity1
msgid "Financing Activities"
msgstr "أنشطة التمويل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__followup_id
msgid "Follow Ups"
msgstr "المتابعات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__name
msgid "Follow-Up Action"
msgstr "اتبع الحركة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__followup_line_ids
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_tree
msgid "Follow-up"
msgstr "متابعة"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_followup_line
msgid "Follow-up Criteria"
msgstr "معايير المتابعة"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_followup_menu
msgid "Follow-up Levels"
msgstr "مستويات المتابعة"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_view_list_customer_statements
#: model:ir.ui.menu,name:base_accounting_kit.customer_statements_menu
msgid "Follow-up Reports"
msgstr "تقارير المتابعة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr "متابعة تقارير الشجرة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "خطوات المتابعة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_follower_ids
msgid "Followers"
msgstr "متابعون"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_channel_ids
msgid "Followers (Channels)"
msgstr "المتابعون (القنوات)"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__followup_status
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__followup_status
msgid "Followup status"
msgstr "حالة المتابعة"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_financial_report__sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_account_followup_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"                possible to use print and e-mail templates to send specific messages to\n"
"                the customer."
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/report/account_bank_book.py:0
#: code:addons/base_accounting_kit/report/account_cash_book.py:0
#: code:addons/base_accounting_kit/report/account_day_book.py:0
#: code:addons/base_accounting_kit/report/cash_flow_report.py:0
#: code:addons/base_accounting_kit/report/general_ledger_report.py:0
#: code:addons/base_accounting_kit/report/report_aged_partner.py:0
#: code:addons/base_accounting_kit/report/report_journal_audit.py:0
#: code:addons/base_accounting_kit/report/report_partner_ledger.py:0
#: code:addons/base_accounting_kit/report/report_tax.py:0
#: code:addons/base_accounting_kit/report/report_trial_balance.py:0
#: code:addons/language_translation/base_accounting_kit/report/account_bank_book.py:0
#: code:addons/language_translation/base_accounting_kit/report/account_cash_book.py:0
#: code:addons/language_translation/base_accounting_kit/report/account_day_book.py:0
#: code:addons/language_translation/base_accounting_kit/report/cash_flow_report.py:0
#: code:addons/language_translation/base_accounting_kit/report/general_ledger_report.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_aged_partner.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_journal_audit.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_partner_ledger.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_tax.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_trial_balance.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "محتوى النموذج مفقود ، لا يمكن طباعة هذا التقرير."

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__view_format
msgid "Format"
msgstr "شكل"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr "من الآن فصاعدًا ، قد ترغب في:"

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_general_ledger_menu
#: model:ir.actions.report,name:base_accounting_kit.action_report_general_ledger
#: model:ir.ui.menu,name:base_accounting_kit.menu_general_ledger
msgid "General Ledger"
msgstr "دفتر الأستاذ العام"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_report_general_ledger
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_general_ledger
msgid "General Ledger Report"
msgstr "تقرير دفتر الأستاذ العام"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr "إنشاء إدخالات الأصول"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "توليد إدخالات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__journal_state
msgid "Generate Journal As"
msgstr "إنشاء مجلة باسم"

#. module: base_accounting_kit
#: model:ir.actions.server,name:base_accounting_kit.recurring_template_cron_ir_actions_server
#: model:ir.cron,cron_name:base_accounting_kit.recurring_template_cron
#: model:ir.cron,name:base_accounting_kit.recurring_template_cron
msgid "Generate Recurring Entries"
msgstr "إنشاء إدخالات متكررة"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_reports_generic_statements
msgid "Generic Statements"
msgstr "بيانات عامة"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_followup_line__sequence
msgid "Gives the sequence order when displaying a list of follow-up lines."
msgstr "يعطي ترتيب التسلسل عند عرض قائمة سطور المتابعة."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr "انتقل إلى كشوف الحسابات البنكية"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Good Job!"
msgstr "عمل جيد!"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__gross_value
msgid "Gross Amount"
msgstr "المبلغ الإجمالي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__value
msgid "Gross Value"
msgstr "القيمة الإجمالية"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr "القيمة الإجمالية للأصول"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Group By"
msgstr "مجموعة من"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Group By..."
msgstr "مجموعة من..."

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__group_entries
msgid "Group Journal Entries"
msgstr "إدخالات دفتر اليومية المجموعة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__has_due
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__has_due
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__has_due
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__has_due
msgid "Has Due"
msgstr "مستحق"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__financial_report__view_format__horizontal
msgid "Horizontal"
msgstr "عرضي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_account__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_journal__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_reconciliation_widget__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_day_book_report_template__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_agedpartnerbalance__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_bank_book__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_book__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_flow__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_financial__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_general_ledger__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_journal_audit__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_partnerledger__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_tax__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_trial_balance__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_config_settings__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__id
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__id
msgid "ID"
msgstr "المُعرف"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "INVOICES"
msgstr "الفواتير"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_needaction
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا تم تحديدها ، فإن الرسائل الجديدة تتطلب انتباهك."

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_has_error
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا تم تحديده ، فإن بعض الرسائل بها خطأ في التسليم."

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_bank_book_report__initial_balance
#: model:ir.model.fields,help:base_accounting_kit.field_account_cash_book_report__initial_balance
#: model:ir.model.fields,help:base_accounting_kit.field_account_report_general_ledger__initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__res_partner__followup_status__in_need_of_action
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_search_view
msgid "In need of action"
msgstr "بحاجة للعمل"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_payment.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_payment.py:0
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__initial_balance
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__initial_balance
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__initial_balance
msgid "Include Initial Balances"
msgstr "قم بتضمين الأرصدة الأولية"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_income0
msgid "Income"
msgstr "الإيرادات"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Income/Expense"
msgstr "الدخل / المصاريف"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first January / Start date of fiscal "
"year"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_investing_activity0
msgid "Investing Activities"
msgstr "نشاطات إستثمارية"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__invoice_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#, python-format
msgid "Invoice"
msgstr "فاتورة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__invoice_list
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__invoice_list
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_form_view
msgid "Invoice Details"
msgstr "تفاصيل الفاتورة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_is_follower
msgid "Is Follower"
msgstr "أتباع"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__is_warning
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__is_warning
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__is_warning
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__is_warning
msgid "Is Warning"
msgstr "هو تحذير"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_report_partner_ledger__amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "إنه المبلغ الذي تخطط للحصول عليه ولا يمكنك استهلاكه."

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__5
msgid "Italic Text (smaller)"
msgstr "نص مائل (أصغر)"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.payment_matching_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Items"
msgstr "العناصر"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
msgid "JRNL"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model,name:base_accounting_kit.model_account_journal
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__journal_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__journal_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#, python-format
msgid "Journal"
msgstr "دفتر اليومية"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__sortby__sort_journal_partner
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__sortby__sort_journal_partner
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__sortby__sort_journal_partner
msgid "Journal & Partner"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr "إدخالات دفتر اليومية"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_print_journal__sort_selection__move_name
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Journal Entry Number"
msgstr "رقم قيد اليومية"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid "Journal Items"
msgstr "عناصر دفتر اليومية"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_matching.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_matching.js:0
#, python-format
msgid "Journal Items to Reconcile"
msgstr "عناصر دفتر اليومية المطلوب التوفيق بينها"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_journal_audit
msgid "Journal Report"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
msgid "Journal and Partner"
msgstr "مجلة وشريك"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__journal_ids
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__journal_ids
msgid "Journals"
msgstr "دفاتر اليومية"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_print_journal_menu
#: model:ir.actions.report,name:base_accounting_kit.action_report_journal
#: model:ir.ui.menu,name:base_accounting_kit.menu_print_journal
msgid "Journals Audit"
msgstr "تدقيق المجلات"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#, python-format
msgid "Label"
msgstr "ضع الكلمة المناسبة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_account____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_journal____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment_register____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_reconciliation_widget____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_product_template____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_day_book_report_template____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_agedpartnerbalance____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_bank_book____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_book____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_cash_flow____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_financial____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_general_ledger____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_journal_audit____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_partnerledger____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_tax____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_report_base_accounting_kit_report_trial_balance____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner____last_update
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Last Month"
msgstr "الشهر الماضي"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr "آخر تسوية:"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__write_uid
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_followup__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_depreciation_confirmation_wizard__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__write_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Last Year"
msgstr "العام الماضي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__level
msgid "Level"
msgstr "مستوى"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_liability0
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_liabilitysum0
msgid "Liability"
msgstr "مسؤولية"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__method__linear
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__method__linear
msgid "Linear"
msgstr "خطي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__move_check
msgid "Linked"
msgstr "مرتبط"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Load more"
msgstr "تحميل المزيد"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Load more... ("
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__fiscalyear_lock_date
msgid "Lock Date"
msgstr "تاريخ القفل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_lock_date__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "تاريخ القفل لغير المستشارين"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.menu_lock_dates
msgid "Lock Dates"
msgstr "تواريخ القفل"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_lock_date
msgid "Lock date for accounting"
msgstr "تاريخ القفل للمحاسبة"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.account_update_lock_date_act_window
msgid "Lock your Fiscal Period"
msgstr "قفل الفترة المالية الخاصة بك"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__1
msgid "Main Title 1 (bold, underlined)"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Manual Operations"
msgstr "العمليات اليدوية"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr "تطابق مع الإدخالات التي ليست من حسابات القبض / المدفوعات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسالة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr "مطابقة متنوعة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
msgid "Modify"
msgstr "تعديل"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_asset_modify
#: model:ir.model,name:base_accounting_kit.model_asset_modify
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
msgid "Modify Asset"
msgstr "تعديل الأصل"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr "تعديل الإهلاك"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Modify models"
msgstr "تعديل النماذج"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_search
msgid "Month"
msgstr "شهر"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move_line__asset_mrr
msgid "Monthly Recurring Revenue"
msgstr "الإيرادات الشهرية المتكررة"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__recurring_period__months
msgid "Months"
msgstr "الشهور"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Move"
msgstr "نقل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__template_name
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__name
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_financial
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Name"
msgstr "اسم"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "Net"
msgstr "شبكة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Net Profit or Loss"
msgstr "صافي الربح أو الخسارة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "New"
msgstr "جديد"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__remaining_value
msgid "Next Period Depreciation"
msgstr "إهلاك الفترة التالية"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__next_reminder_date
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__next_reminder_date
msgid "Next Reminder Date"
msgstr "تاريخ التذكير التالي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__next_date
msgid "Next Schedule"
msgstr "الجدول التالي"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__cash_flow_report__filter_cmp__filter_no
msgid "No Filters"
msgstr "لا المرشحات"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__res_partner__followup_status__no_action_needed
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_search_view
msgid "No action needed"
msgstr "لا رد فعل مطلوب"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__display_detail__no_detail
msgid "No detail"
msgstr "بدون تفاصيل"

#. module: base_accounting_kit
#: model_terms:ir.actions.act_window,help:base_accounting_kit.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr "لا يوجد متابعة لإرسال!"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_lock_date__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__4
msgid "Normal Text"
msgstr "نص عادي"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Not archived"
msgstr "غير مؤرشف"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__note
#, python-format
msgid "Note"
msgstr "ملحوظة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Nothing to do!"
msgstr "لا شيء لأفعله!"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_number
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_number
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__method_number
msgid "Number of Depreciations"
msgstr "عدد الاهلاك"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__method_time__number
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__method_time__number
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Number of Entries"
msgstr "عدد الادخالات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_period
msgid "Number of Months in a Period"
msgstr "عدد الأشهر في فترة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل التي بها خطأفي  التسليم"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "One Entry Every"
msgstr "دخول واحد كل"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_lock_date__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_model.js:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_model.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Open balance"
msgstr "حساب مفتوح"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_operation0
msgid "Operations"
msgstr "عمليات"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_form_view
msgid "Other Info"
msgstr "معلومات اخرى"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_search_view
msgid "Overdue Invoices"
msgstr "الفواتير المتأخرة"

#. module: base_accounting_kit
#: model:account.payment.method,name:base_accounting_kit.account_payment_method_pdc_in
#: model:account.payment.method,name:base_accounting_kit.account_payment_method_pdc_out
msgid "PDC"
msgstr "PDC"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#, python-format
msgid "Paid"
msgstr "دفع"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__parent_id
msgid "Parent"
msgstr "الأبوين"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_search
msgid "Parent Report"
msgstr "تقرير الوالدين"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__partner_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__partner_id
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__partner_id
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
#, python-format
msgid "Partner"
msgstr "شريك"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_partner_leadger
#: model:ir.actions.report,name:base_accounting_kit.action_report_partnerledger
#: model:ir.ui.menu,name:base_accounting_kit.menu_partner_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
msgid "Partner Ledger"
msgstr "شريك ليدجر"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_partnerledger
msgid "Partner Ledger Report"
msgstr "تقرير دفتر الأستاذ الشريك"

#. module: base_accounting_kit
#: model:ir.ui.menu,name:base_accounting_kit.account_reports_partner
msgid "Partner Reports"
msgstr "تقارير الشركاء"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__result_selection
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__result_selection
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__result_selection
msgid "Partner's"
msgstr "شريك"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Partners"
msgstr "شركاء"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__pay_time__pay_now
msgid "Pay Directly"
msgstr "ادفع مباشرة"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__pay_time__pay_later
msgid "Pay Later"
msgstr "ادفع لاحقا"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__pay_time
msgid "Pay Time"
msgstr "وقت الدفع"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Pay your"
msgstr "دفع الخاص بك"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_aged_trial_balance__result_selection__supplier
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_partner_report__result_selection__supplier
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_partner_ledger__result_selection__supplier
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Payable Accounts"
msgstr "حسابات الدفع"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_followup_definition_form
msgid "Payment Follow-ups"
msgstr "متابعة الدفع"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.matching_account_payment
msgid "Payment Matching"
msgstr "مطابقة الدفع"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_payment
msgid "Payments"
msgstr "المدفوعات"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.payment_matching_view
msgid "Payments Matching"
msgstr "مطابقة المدفوعات"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_payment.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_payment.py:0
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' or 'PDC' selected as payment"
" method and not have already been reconciled"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_payment.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr "بدون عميل لا يمكن مطابقة المدفوعات "

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_period
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__method_period
msgid "Period Length"
msgstr "طول الفترة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__period_length
msgid "Period Length (days)"
msgstr "طول الفترة (أيام)"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Periodicity"
msgstr "دورية"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr "خطوط ما بعد الإهلاك"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_asset.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_asset.js:0
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__move_posted_check
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__move_check
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__journal_state__posted
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "تم النشر"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__posted_value
msgid "Posted Amount"
msgstr "المبلغ المعلن"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "تم نشر خطوط الإهلاك"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__sign__1
msgid "Preserve balance sign"
msgstr "حفظ علامة التوازن"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Presets config"
msgstr "التكوين المسبق"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_bank_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_cash_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_day_book_form_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.financial_report_wiz_modified
msgid "Print"
msgstr "طباعة"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_payment.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_payment.py:0
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "طباعة الشيكات المرقمةمسبقًا"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"طباعة التقرير مع خانة العملة اذا كانت العملة تختلف عن العملة الافتراضية "
"للمؤسسة."

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_product_template
msgid "Product Template"
msgstr "قالب المنتج"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "الربح (الخسارة) للتقرير"

#. module: base_accounting_kit
#: model:account.financial.report,name:base_accounting_kit.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:base_accounting_kit.action_profit_and_loss_report
#: model:ir.ui.menu,name:base_accounting_kit.account_financial_reports_profit_loss
msgid "Profit and Loss"
msgstr "الربح والخسارة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__prorata
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__prorata
msgid "Prorata Temporis"
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid ""
"Prorata temporis can be applied only for time method \"number of "
"depreciations\"."
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Purchase"
msgstr "شراء"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Purchase Month"
msgstr "شهر الشراء"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__type__purchase
msgid "Purchase: Asset"
msgstr "الشراء: الأصول"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_modify__name
msgid "Reason"
msgstr "السبب"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_aged_trial_balance__result_selection__customer
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_partner_report__result_selection__customer
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_partner_ledger__result_selection__customer
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Receivable Accounts"
msgstr "حسابات القبض"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_aged_trial_balance__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_partner_report__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_partner_ledger__result_selection__customer_supplier
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "حسابات القبض والذممالدائنة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Recognition Account"
msgstr "حساب الاعتراف"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr "حساب الدخل الاعتراف"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.payment_matching_view
#, python-format
msgid "Reconcile"
msgstr "التصالح"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__reconciled
msgid "Reconciled Entries"
msgstr "إدخالات تمت تسويتها"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__recurring_interval
msgid "Recurring Interval"
msgstr "الفاصل الزمني المتكرر"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__recurring_lines
msgid "Recurring Lines"
msgstr "خطوط متكررة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__recurring_period
msgid "Recurring Period"
msgstr "فترة متكررة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__recurring_ref
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__recurring_ref
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__recurring_ref
msgid "Recurring Ref"
msgstr "المرجع المتكرر"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_form_view
msgid "Recurring Template"
msgstr "نموذج متكرر"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_recurring_payments_view
#: model:ir.ui.menu,name:base_accounting_kit.account_recurring_payments_child1
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_recurring_payments_tree_view
msgid "Recurring Templates"
msgstr "القوالب المتكررة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.day_book_report_template
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_partnerledger
#, python-format
msgid "Ref"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__code
msgid "Reference"
msgstr "مرجع"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_payment_register
msgid "Register Payment"
msgstr "تسجيل دفعة"

#. module: base_accounting_kit
#: model:followup.line,name:base_accounting_kit.followup_line_id
msgid "Reminder"
msgstr "تذكير"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_form
msgid "Report"
msgstr "نقل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__name
msgid "Report Name"
msgstr "تقرير اسم"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_aged_balance_view
msgid "Report Options"
msgstr "خيارات التقرير"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_financial_report_search
msgid "Report Type"
msgstr "نوع التقرير"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__account_report_id
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__type__account_report
msgid "Report Value"
msgstr "قيمة التقرير"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#, python-format
msgid "Residual"
msgstr "المتبقية"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__value_residual
msgid "Residual Value"
msgstr "القيمة المتبقية"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__sign__-1
msgid "Reverse balance sign"
msgstr "علامة التوازن العكسي"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_asset__state__open
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__state__running
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__asset_asset_report__state__open
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_asset_report_search
msgid "Running"
msgstr "ادارة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة "

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "Sale"
msgstr "تخفيض السعر"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_asset_category__type__sale
msgid "Sale: Revenue Recognition"
msgstr "البيع: الاعتراف بالإيرادات"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Sales"
msgstr "مبيعات"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__salvage_value
msgid "Salvage Value"
msgstr "قيمة مستردة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Save and New"
msgstr "حفظ وجديد"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "فئة أصل البحث"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_filter
msgid "Search Follow-up"
msgstr "متابعة البحث"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#, python-format
msgid "Select Partner"
msgstr "حدد الشريك"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "حدد شريكًا أو اختر نظيرًا"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr "بيع أو التخلص"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__sequence
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__sequence
#: model:ir.model.fields,field_description:base_accounting_kit.field_followup_line__sequence
msgid "Sequence"
msgstr "تسلسل"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "تعيين إلى مسودة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Settings"
msgstr "الإعدادات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__sign
msgid "Sign on Reports"
msgstr "تسجيل الدخول التقارير"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Skill Level: 50%"
msgstr "مستوى المهارة: 50٪"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Skip"
msgstr "تخطى"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__6
msgid "Smallest Text"
msgstr "أصغر نص"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_render.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_render.js:0
#, python-format
msgid "Some fields are undefined"
msgstr "بعض الحقول غير محددة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__sortby
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__sortby
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__sortby
msgid "Sort by"
msgstr "ترتيب حسب"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__date_from
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__date_from
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__date
msgid "Starting Date"
msgstr "تاريخ البدء"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "اذكر هنا الوقت بين عمليتي إهلاك بالأشهر"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_depreciation_line__parent_state
msgid "State of Asset"
msgstr "حالة الأصول"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__state
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_payments__state
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__state
msgid "Status"
msgstr "الحالة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Supplier Invoice"
msgstr "فاتورة المورد"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "TOP 10 CUSTOMERS"
msgstr "أفضل 10 عملاء"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_aged_trial_balance__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_balance_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_book_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_cash_book_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_account_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_common_partner_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_day_book_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_general_ledger__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_cash_flow_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_financial_report__target_move
#: model:ir.model.fields,field_description:base_accounting_kit.field_kit_account_tax_report__target_move
msgid "Target Moves"
msgstr "تحركات الهدف"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "Tax"
msgstr "ضريبة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Tax Amount"
msgstr "قيمة الضريبة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_journal_audit
msgid "Tax Declaration"
msgstr "الإقرار الضريبي"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr "الضريبة مشمولة في السعر"

#. module: base_accounting_kit
#: model:ir.actions.report,name:base_accounting_kit.action_report_account_tax
#: model:ir.model,name:base_accounting_kit.model_kit_account_tax_report
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_tax
#: model:ir.ui.menu,name:base_accounting_kit.menu_tax_report
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_tax
msgid "Tax Report"
msgstr "تقرير الضرائب"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_tax_report
msgid "Tax Reports"
msgstr "تقارير الضرائب"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Taxes"
msgstr "الضرائب"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "That's on average"
msgstr "هذا في المتوسط"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/payment_model.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/payment_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr "المبلغ٪ s ليس مبلغًا جزئيًا صالحًا"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__method_period
msgid "The amount of time between two depreciations, in months"
msgstr "مقدار الوقت بين عمليتي إهلاك بالأشهر"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder.  Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__method_number
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_category__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "عدد الإهلاكات اللازمة لخفض قيمة الأصول الخاصة بك"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_move.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_move.py:0
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be null."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "لا يوجد شيء للتوفيق بينه"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_move_form_inherited
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.header_view
msgid "This Customer's due amount is"
msgstr "المبلغ المستحق لهذا العميل هو"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "This Month"
msgstr "هذا الشهر"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "This Year"
msgstr "هذه السنة"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_move_form_inherited
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.header_view
msgid "This customer's <strong>warning limit</strong> has been crossed."
msgstr "تم تجاوز <strong> حد التحذير </ strong> لهذا العميل."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid ""
"This depreciation is already linked to a journal entry! Please post or "
"delete it."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_cash_flow_report__label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_cash_flow_report__debit_credit
#: model:ir.model.fields,help:base_accounting_kit.field_financial_report__debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "تم تسجيل هذه الدفعة ولكن لم يتم تسويتها."

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period of asset/revenue recognition as well."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__method_time
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__method_time
msgid "Time Method"
msgstr "طريقة الوقت"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "أسلوب الوقت على أساس"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__2
msgid "Title 2 (bold)"
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__style_overwrite__3
msgid "Title 3 (bold, smaller)"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "To Check"
msgstr "للتأكد"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_form
msgid ""
"To remind customers of paying their invoices, you can\n"
"                        define different actions depending on how severely\n"
"                        overdue the customer is. These actions are bundled\n"
"                        into follow-up levels that are triggered when the due\n"
"                        date of an invoice has passed a certain\n"
"                        number of days. If there are other overdue invoices for the\n"
"                        same customer, the actions of the most\n"
"                        overdue invoice will be executed."
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr "لتسريع المصالحة ، حدد"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_tree_view
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_agedpartnerbalance
msgid "Total"
msgstr "مجموع"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__total_due
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__total_due
msgid "Total Due"
msgstr "الاجمالي المستحق"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Total Expenses"
msgstr "لمصروفات الكلية"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Total Income"
msgstr "إجمالي الدخل"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__total_overdue
msgid "Total Overdue"
msgstr "إجمالي المتأخرات"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_bank_statement_line__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_move__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_payment__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__due_amount
#: model:ir.model.fields,field_description:base_accounting_kit.field_sale_order__due_amount
msgid "Total Sale"
msgstr "إجمالي البيع"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Transaction"
msgstr "عملية تجارية"

#. module: base_accounting_kit
#: model:ir.actions.act_window,name:base_accounting_kit.action_account_balance_menu
#: model:ir.actions.report,name:base_accounting_kit.action_report_trial_balance
#: model:ir.ui.menu,name:base_accounting_kit.menu_Balance_report
msgid "Trial Balance"
msgstr "ميزان المراجعة"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_account_balance_report
#: model:ir.model,name:base_accounting_kit.model_report_base_accounting_kit_report_trial_balance
msgid "Trial Balance Report"
msgstr "تقرير ميزان المراجعة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__type
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_category__type
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_financial_report__type
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_search
msgid "Type"
msgstr "اكتب"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/report/report_aged_partner.py:0
#: code:addons/language_translation/base_accounting_kit/report/report_aged_partner.py:0
#, python-format
msgid "Unknown Partner"
msgstr "شريك غير معروف"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_asset.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_asset.js:0
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__journal_state__draft
#, python-format
msgid "Unposted"
msgstr "غير منشور"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__unposted_value
msgid "Unposted Amount"
msgstr "المبلغ غير المنشور"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_unread
msgid "Unread Messages"
msgstr "رسائل غير مقروءة"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عداد الرسائل غير المقروءة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#: code:addons/language_translation/base_accounting_kit/static/src/js/account_dashboard.js:0
#, python-format
msgid "Unreconciled"
msgstr "لم تتم التوفيق بينها"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/template.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/template.xml:0
#, python-format
msgid "Unreconciled items"
msgstr "البنود التي لم تتم تسويتها"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.account_update_lock_date_form_view
msgid "Update"
msgstr "تحديث"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Validate"
msgstr "تحقق"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "بائع"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Verify"
msgstr "تحقق"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__financial_report__view_format__vertical
msgid "Vertical"
msgstr "عمودي"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_financial_report__type__sum
msgid "View"
msgstr "رأي"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_partner__warning_stage
#: model:ir.model.fields,field_description:base_accounting_kit.field_res_users__warning_stage
msgid "Warning Amount"
msgstr "مبلغ التحذير"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/credit_limit.py:0
#: code:addons/language_translation/base_accounting_kit/models/credit_limit.py:0
#, python-format
msgid "Warning amount should be less than Blocking amount"
msgstr "يجب أن تكون كمية التحذير أقل من مبلغ الحظر"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_asset_asset__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__website_message_ids
msgid "Website communication history"
msgstr "سجل اتصال الموقع"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__recurring_period__weeks
msgid "Weeks"
msgstr "أسابيع"

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_asset__state
#: model:ir.model.fields,help:base_accounting_kit.field_account_asset_depreciation_line__parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_print_journal__amount_currency
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_report_partner_ledger__amount_currency
msgid "With Currency"
msgstr "مع العملة"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_balance_report__display_account__not_zero
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__display_account__not_zero
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__display_account__not_zero
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_account_report__display_account__not_zero
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__display_account__not_zero
msgid "With balance is not equal to 0"
msgstr "مع رصيد لا يساوي 0"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "With balance not equal to zero"
msgstr "مع رصيد لا يساوي الصفر"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_balance_report__display_account__movement
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_bank_book_report__display_account__movement
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_cash_book_report__display_account__movement
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_common_account_report__display_account__movement
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_report_general_ledger__display_account__movement
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_bank_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_cash_book
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_general_ledger
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.report_trial_balance
msgid "With movements"
msgstr "مع الحركات"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "مع الفواتير المتأخرة"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#: code:addons/language_translation/base_accounting_kit/models/payment_matching.py:0
#, python-format
msgid "Write-Off"
msgstr "لا تصلح"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "Writeoff Date"
msgstr "تاريخ الشطب"

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_asset_asset_report__name
msgid "Year"
msgstr "عام"

#. module: base_accounting_kit
#: model:ir.model.fields.selection,name:base_accounting_kit.selection__account_recurring_payments__recurring_period__years
msgid "Years"
msgstr "سنوات"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/account_lock_date.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/account_lock_date.py:0
#, python-format
msgid "You are not allowed to execute this action."
msgstr "لا يسمح لك بتنفيذ هذا الإجراء."

#. module: base_accounting_kit
#: model:ir.model.fields,help:base_accounting_kit.field_account_financial_report__style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document is in %s state."
msgstr "لا يمكنك حذف مستند في حالة٪ s."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr "لا يمكنك حذف مستند يحتوي على مدخلات تم ترحيلها."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr "لا يمكنك حذف بنود الإهلاك التي تم ترحيلها."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/models/account_asset.py:0
#: code:addons/language_translation/base_accounting_kit/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted installment lines."
msgstr "لا يمكنك حذف سطور الأقساط المنشورة."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/account_bank_book_wizard.py:0
#: code:addons/base_accounting_kit/wizard/account_cash_book_wizard.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/account_bank_book_wizard.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/account_cash_book_wizard.py:0
#, python-format
msgid "You must choose a Start Date"
msgstr "يجب عليك اختيار تاريخ البدء"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/general_ledger.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/general_ledger.py:0
#, python-format
msgid "You must define a Start Date"
msgstr "يجب عليك تحديد تاريخ البدء"

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/aged_partner.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/aged_partner.py:0
#, python-format
msgid "You must set a period length greater than 0."
msgstr "يجب عليك تعيين طول فترة أكبر من 0."

#. module: base_accounting_kit
#: code:addons/base_accounting_kit/wizard/aged_partner.py:0
#: code:addons/language_translation/base_accounting_kit/wizard/aged_partner.py:0
#, python-format
msgid "You must set a start date."
msgstr "يجب عليك تحديد تاريخ البدء."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "You reconciled"
msgstr "أنت تصالح"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "and follow-up customers"
msgstr "ومتابعة العملاء"

#. module: base_accounting_kit
#: model:ir.model,name:base_accounting_kit.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr "الأيام المتأخرة ، قم بالإجراءات التالية:"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "e.g. Computers"
msgstr "على سبيل المثال أجهزة الكمبيوتر"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr "على سبيل المثال كمبيوتر محمول iBook"

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_form_view
msgid "has no due amount."
msgstr "ليس له مبلغ مستحق."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr "تم التوفيق بينها تلقائيًا."

#. module: base_accounting_kit
#: model:ir.model.fields,field_description:base_accounting_kit.field_account_recurring_entries_line__tmpl_id
msgid "id"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.asset_modify_form
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.view_account_asset_category_form
msgid "months"
msgstr "الشهور"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "reconcile"
msgstr "التصالح"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "reconciliation models"
msgstr "نماذج المصالحة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "remaining)"
msgstr ""

#. module: base_accounting_kit
#: model_terms:ir.ui.view,arch_db:base_accounting_kit.customer_statements_form_view
msgid "report"
msgstr "نقل"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "ثواني لكل معاملة."

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "statement lines"
msgstr "خطوط البيان"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "transactions in"
msgstr ""

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "فواتير غير مدفوعة"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "unreconciled entries"
msgstr "إدخالات لم تتم تسويتها"

#. module: base_accounting_kit
#. openerp-web
#: code:addons/base_accounting_kit/static/src/xml/payment_matching.xml:0
#: code:addons/language_translation/base_accounting_kit/static/src/xml/payment_matching.xml:0
#, python-format
msgid "vendor bills"
msgstr "فواتير البائعين"
