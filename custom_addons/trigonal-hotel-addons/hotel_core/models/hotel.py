from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class Hotel(models.Model):
    _name = "hotel.hotel"
    _description = "Description"
    _check_company_auto = True

    company_id = fields.Many2one(
        "res.company", required=True, default=lambda self: self.env.company
    )
    branch_id = fields.Many2one("hotel.branch", required=True, check_company=True)
    name = fields.Char(string="Hotel Name", required=True)
    is_default = fields.Boolean(string="Default Hotel?", required=True, default=False)
    active = fields.Boolean(string="Is Active?", required=True, default=True)
    reservations = fields.One2many(
        "hotel.reservation", "hotel_id", string="Reservations"
    )

    def _multiple_hotel(self):
        _logger.info(
            self.env["ir.config_parameter"]
            .sudo()
            .get_param("hotel_core.enable_multi_hotel")
        )
        return (
            self.env["ir.config_parameter"]
            .sudo()
            .get_param("hotel_core.enable_multi_hotel")
        )

    multiple_hotel = fields.Boolean(
        "Multiple Hotel",
        default=_multiple_hotel,
        required=True,
        store=False,
    )

    def get_default_hotel(self):
        return self.search([("is_default", "=", True)])

    def action_view_hotel_reservation(self):
        return {
            "name": _("Hotels"),
            "view_mode": "tree",
            "res_model": "hotel.reservation",
            "view_id": False,
            "type": "ir.actions.act_window",
            "domain": [
                ("hotel_id", "=", self.id),
                ("state", "in", ["done", "checkout"]),
            ],
        }

    def _compute_reservation_count(self):
        for hotel in self:
            hotel.reservation_count = len(
                list(
                    filter(
                        lambda res: res.state in ["done", "checkout"],
                        hotel.reservations,
                    )
                )
            )

    reservation_count = fields.Integer(
        compute="_compute_reservation_count", string="Hotels"
    )

    @api.model_create_multi
    def create(self, vals_list):
        if not isinstance(vals_list, list):
            vals_list = [vals_list]
        
        count_hotel = len(self.env["hotel.hotel"].search([]))
        _logger.info(count_hotel)
        _logger.info(self.multiple_hotel)
        
        # Check multi-hotel configuration
        multi_hotel_enabled = self.env["ir.config_parameter"].sudo().get_param("hotel_core.enable_multi_hotel", "False")
        if (
            count_hotel > 0
            and multi_hotel_enabled not in ["True", "true", True]
        ):
            raise ValidationError("Failed: Enable multihotel")
        
        # Handle default hotel setting
        default_hotel = self.env["hotel.hotel"].search([("is_default", "=", True)])
        for vals in vals_list:
            if vals.get("is_default"):
                if default_hotel:
                    default_hotel.is_default = False
        
        return super(Hotel, self).create(vals_list)

    def write(self, vals):
        if "is_default" in vals and vals["is_default"] == True:
            # Check whether there are other default hotels, if yes throw error
            default_hotel = self.env["hotel.hotel"].search([("is_default", "=", True)])
            if default_hotel and default_hotel.id != self.id:
                default_hotel.is_default = False
        return super(Hotel, self).write(vals)

    def action_archive(self):
        res = ""
        for hotel in self:
            active_reservations = self.env["hotel.reservation"].search(
                [("active", "=", True), ("hotel_id", "=", hotel.id)]
            )
            if active_reservations:
                raise ValidationError(
                    "Hotel Reservations are active. Archive all the reservations to remove hotel."
                )

            res = super(Hotel, self).action_archive()

        return res

    def action_unarchive(self):
        count_hotel = len(self.env["hotel.hotel"].search([]))
        multi_hotel_enabled = self.env["ir.config_parameter"].sudo().get_param("hotel_core.enable_multi_hotel", "False")
        if (
            count_hotel > 0
            and multi_hotel_enabled not in ["True", "true", True]
        ):
            raise ValidationError("Failed: Enable Multihotel")
        return super().action_unarchive()

    def unlink(self):
        res = ""
        for hotel in self:
            active_reservations = self.env["hotel.reservation"].search(
                [("active", "=", True), ("hotel_id", "=", hotel.id)]
            )
            if active_reservations:
                raise ValidationError(
                    "Hotel Reservations are active. Archive all the reservations to remove hotel."
                )
            res = hotel.write({"active": False})
        return res
