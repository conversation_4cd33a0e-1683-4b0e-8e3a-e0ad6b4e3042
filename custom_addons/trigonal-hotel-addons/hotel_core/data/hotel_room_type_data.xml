<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- Hotel Room Type Records -->
    <record id="room_type_standard" model="hotel.room.type">
        <field name="name">Standard Room</field>
        <field name="active">True</field>
    </record>

    <record id="room_type_deluxe" model="hotel.room.type">
        <field name="name">Deluxe Room</field>
        <field name="active">True</field>
    </record>

    <record id="room_type_suite" model="hotel.room.type">
        <field name="name">Suite</field>
        <field name="active">True</field>
    </record>

    <record id="room_type_executive" model="hotel.room.type">
        <field name="name">Executive Room</field>
        <field name="active">True</field>
    </record>

    <record id="room_type_presidential" model="hotel.room.type">
        <field name="name">Presidential Suite</field>
        <field name="active">True</field>
    </record>

    <record id="room_type_family" model="hotel.room.type">
        <field name="name">Family Room</field>
        <field name="active">True</field>
    </record>

    <record id="room_type_single" model="hotel.room.type">
        <field name="name">Single Room</field>
        <field name="active">True</field>
    </record>

    <record id="room_type_double" model="hotel.room.type">
        <field name="name">Double Room</field>
        <field name="active">True</field>
    </record>

    <record id="room_type_twin" model="hotel.room.type">
        <field name="name">Twin Room</field>
        <field name="active">True</field>
    </record>

    <record id="room_type_penthouse" model="hotel.room.type">
        <field name="name">Penthouse Suite</field>
        <field name="active">True</field>
    </record>

</odoo>
