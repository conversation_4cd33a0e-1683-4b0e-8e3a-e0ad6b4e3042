<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- Hotel Room Records for Grand Plaza Hotel -->
    
    <!-- Ground Floor Rooms -->
    <record id="room_101" model="hotel.room">
        <field name="name">Room 101</field>
        <field name="floor_id" ref="floor_ground"/>
        <field name="room_categ_id" ref="room_type_standard"/>
        <field name="max_adult">2</field>
        <field name="max_child">1</field>
        <field name="capacity">3</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">120.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_shower')])]"/>
    </record>

    <record id="room_102" model="hotel.room">
        <field name="name">Room 102</field>
        <field name="floor_id" ref="floor_ground"/>
        <field name="room_categ_id" ref="room_type_standard"/>
        <field name="max_adult">2</field>
        <field name="max_child">1</field>
        <field name="capacity">3</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">120.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_shower')])]"/>
    </record>

    <record id="room_103" model="hotel.room">
        <field name="name">Room 103</field>
        <field name="floor_id" ref="floor_ground"/>
        <field name="room_categ_id" ref="room_type_deluxe"/>
        <field name="max_adult">2</field>
        <field name="max_child">2</field>
        <field name="capacity">4</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">180.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_bathtub'), ref('amenity_minibar')])]"/>
    </record>

    <!-- First Floor Rooms -->
    <record id="room_201" model="hotel.room">
        <field name="name">Room 201</field>
        <field name="floor_id" ref="floor_first"/>
        <field name="room_categ_id" ref="room_type_deluxe"/>
        <field name="max_adult">2</field>
        <field name="max_child">2</field>
        <field name="capacity">4</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">180.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_bathtub'), ref('amenity_minibar'), ref('amenity_balcony')])]"/>
    </record>

    <record id="room_202" model="hotel.room">
        <field name="name">Room 202</field>
        <field name="floor_id" ref="floor_first"/>
        <field name="room_categ_id" ref="room_type_suite"/>
        <field name="max_adult">4</field>
        <field name="max_child">2</field>
        <field name="capacity">6</field>
        <field name="number_of_beds">2</field>
        <field name="status">available</field>
        <field name="list_price">350.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_bathtub'), ref('amenity_minibar'), ref('amenity_balcony'), ref('amenity_kitchenette')])]"/>
    </record>

    <record id="room_203" model="hotel.room">
        <field name="name">Room 203</field>
        <field name="floor_id" ref="floor_first"/>
        <field name="room_categ_id" ref="room_type_family"/>
        <field name="max_adult">4</field>
        <field name="max_child">3</field>
        <field name="capacity">7</field>
        <field name="number_of_beds">3</field>
        <field name="status">available</field>
        <field name="list_price">280.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_shower'), ref('amenity_refrigerator')])]"/>
    </record>

    <!-- Second Floor Rooms -->
    <record id="room_301" model="hotel.room">
        <field name="name">Room 301</field>
        <field name="floor_id" ref="floor_second"/>
        <field name="room_categ_id" ref="room_type_executive"/>
        <field name="max_adult">2</field>
        <field name="max_child">1</field>
        <field name="capacity">3</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">250.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_bathtub'), ref('amenity_minibar'), ref('amenity_safe'), ref('amenity_balcony')])]"/>
    </record>

    <record id="room_302" model="hotel.room">
        <field name="name">Room 302</field>
        <field name="floor_id" ref="floor_second"/>
        <field name="room_categ_id" ref="room_type_executive"/>
        <field name="max_adult">2</field>
        <field name="max_child">1</field>
        <field name="capacity">3</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">250.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_bathtub'), ref('amenity_minibar'), ref('amenity_safe'), ref('amenity_balcony')])]"/>
    </record>

    <!-- Third Floor Rooms -->
    <record id="room_401" model="hotel.room">
        <field name="name">Room 401</field>
        <field name="floor_id" ref="floor_third"/>
        <field name="room_categ_id" ref="room_type_suite"/>
        <field name="max_adult">4</field>
        <field name="max_child">2</field>
        <field name="capacity">6</field>
        <field name="number_of_beds">2</field>
        <field name="status">available</field>
        <field name="list_price">400.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_bathtub'), ref('amenity_minibar'), ref('amenity_safe'), ref('amenity_balcony'), ref('amenity_kitchenette'), ref('amenity_netflix')])]"/>
    </record>

    <!-- Penthouse Floor -->
    <record id="room_penthouse" model="hotel.room">
        <field name="name">Penthouse Suite</field>
        <field name="floor_id" ref="floor_penthouse"/>
        <field name="room_categ_id" ref="room_type_penthouse"/>
        <field name="max_adult">6</field>
        <field name="max_child">4</field>
        <field name="capacity">10</field>
        <field name="number_of_beds">3</field>
        <field name="status">available</field>
        <field name="list_price">800.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_bathtub'), ref('amenity_minibar'), ref('amenity_safe'), ref('amenity_balcony'), ref('amenity_kitchenette'), ref('amenity_netflix'), ref('amenity_music_system')])]"/>
    </record>

    <!-- Additional Rooms for variety -->
    <record id="room_104" model="hotel.room">
        <field name="name">Room 104</field>
        <field name="floor_id" ref="floor_ground"/>
        <field name="room_categ_id" ref="room_type_single"/>
        <field name="max_adult">1</field>
        <field name="max_child">0</field>
        <field name="capacity">1</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">80.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_shower')])]"/>
    </record>

    <record id="room_105" model="hotel.room">
        <field name="name">Room 105</field>
        <field name="floor_id" ref="floor_ground"/>
        <field name="room_categ_id" ref="room_type_double"/>
        <field name="max_adult">2</field>
        <field name="max_child">1</field>
        <field name="capacity">3</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">140.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_shower'), ref('amenity_coffee_maker')])]"/>
    </record>

    <record id="room_106" model="hotel.room">
        <field name="name">Room 106</field>
        <field name="floor_id" ref="floor_ground"/>
        <field name="room_categ_id" ref="room_type_twin"/>
        <field name="max_adult">2</field>
        <field name="max_child">0</field>
        <field name="capacity">2</field>
        <field name="number_of_beds">2</field>
        <field name="status">available</field>
        <field name="list_price">160.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_shower'), ref('amenity_safe')])]"/>
    </record>

    <!-- More First Floor Rooms -->
    <record id="room_204" model="hotel.room">
        <field name="name">Room 204</field>
        <field name="floor_id" ref="floor_first"/>
        <field name="room_categ_id" ref="room_type_deluxe"/>
        <field name="max_adult">2</field>
        <field name="max_child">2</field>
        <field name="capacity">4</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">180.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_bathtub'), ref('amenity_minibar'), ref('amenity_balcony')])]"/>
    </record>

    <record id="room_205" model="hotel.room">
        <field name="name">Room 205</field>
        <field name="floor_id" ref="floor_first"/>
        <field name="room_categ_id" ref="room_type_standard"/>
        <field name="max_adult">2</field>
        <field name="max_child">1</field>
        <field name="capacity">3</field>
        <field name="number_of_beds">1</field>
        <field name="status">available</field>
        <field name="list_price">120.00</field>
        <field name="active">True</field>
        <field name="room_amenities_ids" eval="[(6, 0, [ref('amenity_wifi'), ref('amenity_tv'), ref('amenity_ac'), ref('amenity_shower')])]"/>
    </record>

    <!-- Hotel Room Bed Records -->
    <record id="bed_room_101" model="hotel.room.bed">
        <field name="room_id" ref="room_101"/>
        <field name="bed_type" ref="hotel_bed_type_double"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_102" model="hotel.room.bed">
        <field name="room_id" ref="room_102"/>
        <field name="bed_type" ref="hotel_bed_type_double"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_103" model="hotel.room.bed">
        <field name="room_id" ref="room_103"/>
        <field name="bed_type" ref="hotel_bed_type_queen"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_104" model="hotel.room.bed">
        <field name="room_id" ref="room_104"/>
        <field name="bed_type" ref="hotel_bed_type_single"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_105" model="hotel.room.bed">
        <field name="room_id" ref="room_105"/>
        <field name="bed_type" ref="hotel_bed_type_double"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_106_1" model="hotel.room.bed">
        <field name="room_id" ref="room_106"/>
        <field name="bed_type" ref="hotel_bed_type_single"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_106_2" model="hotel.room.bed">
        <field name="room_id" ref="room_106"/>
        <field name="bed_type" ref="hotel_bed_type_single"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_201" model="hotel.room.bed">
        <field name="room_id" ref="room_201"/>
        <field name="bed_type" ref="hotel_bed_type_queen"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_202_1" model="hotel.room.bed">
        <field name="room_id" ref="room_202"/>
        <field name="bed_type" ref="hotel_bed_type_king"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_202_2" model="hotel.room.bed">
        <field name="room_id" ref="room_202"/>
        <field name="bed_type" ref="hotel_bed_type_double"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_penthouse_1" model="hotel.room.bed">
        <field name="room_id" ref="room_penthouse"/>
        <field name="bed_type" ref="hotel_bed_type_king"/>
        <field name="number">2</field>
        <field name="active">True</field>
    </record>

    <record id="bed_room_penthouse_2" model="hotel.room.bed">
        <field name="room_id" ref="room_penthouse"/>
        <field name="bed_type" ref="hotel_bed_type_double"/>
        <field name="number">1</field>
        <field name="active">True</field>
    </record>

</odoo>
