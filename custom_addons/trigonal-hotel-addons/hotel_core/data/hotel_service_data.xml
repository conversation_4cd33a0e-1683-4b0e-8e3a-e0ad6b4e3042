<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- Hotel Service Type Records -->
    <record id="service_type_food_beverage" model="hotel.service.type">
        <field name="name">Food & Beverage</field>
        <field name="active">True</field>
    </record>

    <record id="service_type_housekeeping" model="hotel.service.type">
        <field name="name">Housekeeping</field>
        <field name="active">True</field>
    </record>

    <record id="service_type_transportation" model="hotel.service.type">
        <field name="name">Transportation</field>
        <field name="active">True</field>
    </record>

    <record id="service_type_spa_wellness" model="hotel.service.type">
        <field name="name">Spa & Wellness</field>
        <field name="active">True</field>
    </record>

    <record id="service_type_business" model="hotel.service.type">
        <field name="name">Business Services</field>
        <field name="active">True</field>
    </record>

    <record id="service_type_recreation" model="hotel.service.type">
        <field name="name">Recreation</field>
        <field name="active">True</field>
    </record>

    <!-- Hotel Services Records -->
    <!-- Food & Beverage Services -->
    <record id="service_room_service" model="hotel.services">
        <field name="name">24/7 Room Service</field>
        <field name="service_categ_id" ref="service_type_food_beverage"/>
        <field name="list_price">25.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_breakfast" model="hotel.services">
        <field name="name">Continental Breakfast</field>
        <field name="service_categ_id" ref="service_type_food_beverage"/>
        <field name="list_price">15.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_minibar" model="hotel.services">
        <field name="name">Mini Bar Service</field>
        <field name="service_categ_id" ref="service_type_food_beverage"/>
        <field name="list_price">10.00</field>
        <field name="active">True</field>
    </record>

    <!-- Housekeeping Services -->
    <record id="service_laundry" model="hotel.services">
        <field name="name">Laundry Service</field>
        <field name="service_categ_id" ref="service_type_housekeeping"/>
        <field name="list_price">20.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_dry_cleaning" model="hotel.services">
        <field name="name">Dry Cleaning</field>
        <field name="service_categ_id" ref="service_type_housekeeping"/>
        <field name="list_price">30.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_extra_cleaning" model="hotel.services">
        <field name="name">Extra Room Cleaning</field>
        <field name="service_categ_id" ref="service_type_housekeeping"/>
        <field name="list_price">15.00</field>
        <field name="active">True</field>
    </record>

    <!-- Transportation Services -->
    <record id="service_airport_pickup" model="hotel.services">
        <field name="name">Airport Pickup</field>
        <field name="service_categ_id" ref="service_type_transportation"/>
        <field name="list_price">50.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_airport_drop" model="hotel.services">
        <field name="name">Airport Drop-off</field>
        <field name="service_categ_id" ref="service_type_transportation"/>
        <field name="list_price">50.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_taxi" model="hotel.services">
        <field name="name">Taxi Service</field>
        <field name="service_categ_id" ref="service_type_transportation"/>
        <field name="list_price">35.00</field>
        <field name="active">True</field>
    </record>

    <!-- Spa & Wellness Services -->
    <record id="service_massage" model="hotel.services">
        <field name="name">Full Body Massage</field>
        <field name="service_categ_id" ref="service_type_spa_wellness"/>
        <field name="list_price">80.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_facial" model="hotel.services">
        <field name="name">Facial Treatment</field>
        <field name="service_categ_id" ref="service_type_spa_wellness"/>
        <field name="list_price">60.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_gym" model="hotel.services">
        <field name="name">Gym Access</field>
        <field name="service_categ_id" ref="service_type_spa_wellness"/>
        <field name="list_price">10.00</field>
        <field name="active">True</field>
    </record>

    <!-- Business Services -->
    <record id="service_meeting_room" model="hotel.services">
        <field name="name">Meeting Room (per hour)</field>
        <field name="service_categ_id" ref="service_type_business"/>
        <field name="list_price">75.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_printing" model="hotel.services">
        <field name="name">Printing & Copying</field>
        <field name="service_categ_id" ref="service_type_business"/>
        <field name="list_price">5.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_internet" model="hotel.services">
        <field name="name">Premium Internet</field>
        <field name="service_categ_id" ref="service_type_business"/>
        <field name="list_price">15.00</field>
        <field name="active">True</field>
    </record>

    <!-- Recreation Services -->
    <record id="service_pool" model="hotel.services">
        <field name="name">Swimming Pool Access</field>
        <field name="service_categ_id" ref="service_type_recreation"/>
        <field name="list_price">12.00</field>
        <field name="active">True</field>
    </record>

    <record id="service_tennis" model="hotel.services">
        <field name="name">Tennis Court (per hour)</field>
        <field name="service_categ_id" ref="service_type_recreation"/>
        <field name="list_price">40.00</field>
        <field name="active">True</field>
    </record>

</odoo>
