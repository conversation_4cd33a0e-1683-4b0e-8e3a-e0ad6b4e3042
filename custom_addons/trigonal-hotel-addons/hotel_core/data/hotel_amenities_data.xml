<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- Hotel Room Amenities Type Records -->
    <record id="amenity_type_technology" model="hotel.room.amenities.type">
        <field name="name">Technology</field>
        <field name="active">True</field>
    </record>

    <record id="amenity_type_comfort" model="hotel.room.amenities.type">
        <field name="name">Comfort</field>
        <field name="active">True</field>
    </record>

    <record id="amenity_type_bathroom" model="hotel.room.amenities.type">
        <field name="name">Bathroom</field>
        <field name="active">True</field>
    </record>

    <record id="amenity_type_entertainment" model="hotel.room.amenities.type">
        <field name="name">Entertainment</field>
        <field name="active">True</field>
    </record>

    <record id="amenity_type_kitchen" model="hotel.room.amenities.type">
        <field name="name">Kitchen</field>
        <field name="active">True</field>
    </record>

    <!-- Hotel Room Amenities Records -->
    <!-- Technology Amenities -->
    <record id="amenity_wifi" model="hotel.room.amenities">
        <field name="name">Free WiFi</field>
        <field name="amenity_categ_id" ref="amenity_type_technology"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_tv" model="hotel.room.amenities">
        <field name="name">LED TV</field>
        <field name="amenity_categ_id" ref="amenity_type_technology"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_cable_tv" model="hotel.room.amenities">
        <field name="name">Cable TV</field>
        <field name="amenity_categ_id" ref="amenity_type_technology"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_phone" model="hotel.room.amenities">
        <field name="name">Telephone</field>
        <field name="amenity_categ_id" ref="amenity_type_technology"/>
        <field name="active">True</field>
    </record>

    <!-- Comfort Amenities -->
    <record id="amenity_ac" model="hotel.room.amenities">
        <field name="name">Air Conditioning</field>
        <field name="amenity_categ_id" ref="amenity_type_comfort"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_heating" model="hotel.room.amenities">
        <field name="name">Heating</field>
        <field name="amenity_categ_id" ref="amenity_type_comfort"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_minibar" model="hotel.room.amenities">
        <field name="name">Mini Bar</field>
        <field name="amenity_categ_id" ref="amenity_type_comfort"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_safe" model="hotel.room.amenities">
        <field name="name">In-room Safe</field>
        <field name="amenity_categ_id" ref="amenity_type_comfort"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_balcony" model="hotel.room.amenities">
        <field name="name">Balcony</field>
        <field name="amenity_categ_id" ref="amenity_type_comfort"/>
        <field name="active">True</field>
    </record>

    <!-- Bathroom Amenities -->
    <record id="amenity_bathtub" model="hotel.room.amenities">
        <field name="name">Bathtub</field>
        <field name="amenity_categ_id" ref="amenity_type_bathroom"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_shower" model="hotel.room.amenities">
        <field name="name">Shower</field>
        <field name="amenity_categ_id" ref="amenity_type_bathroom"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_hairdryer" model="hotel.room.amenities">
        <field name="name">Hair Dryer</field>
        <field name="amenity_categ_id" ref="amenity_type_bathroom"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_toiletries" model="hotel.room.amenities">
        <field name="name">Complimentary Toiletries</field>
        <field name="amenity_categ_id" ref="amenity_type_bathroom"/>
        <field name="active">True</field>
    </record>

    <!-- Entertainment Amenities -->
    <record id="amenity_netflix" model="hotel.room.amenities">
        <field name="name">Netflix</field>
        <field name="amenity_categ_id" ref="amenity_type_entertainment"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_music_system" model="hotel.room.amenities">
        <field name="name">Music System</field>
        <field name="amenity_categ_id" ref="amenity_type_entertainment"/>
        <field name="active">True</field>
    </record>

    <!-- Kitchen Amenities -->
    <record id="amenity_kitchenette" model="hotel.room.amenities">
        <field name="name">Kitchenette</field>
        <field name="amenity_categ_id" ref="amenity_type_kitchen"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_microwave" model="hotel.room.amenities">
        <field name="name">Microwave</field>
        <field name="amenity_categ_id" ref="amenity_type_kitchen"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_refrigerator" model="hotel.room.amenities">
        <field name="name">Refrigerator</field>
        <field name="amenity_categ_id" ref="amenity_type_kitchen"/>
        <field name="active">True</field>
    </record>

    <record id="amenity_coffee_maker" model="hotel.room.amenities">
        <field name="name">Coffee Maker</field>
        <field name="amenity_categ_id" ref="amenity_type_kitchen"/>
        <field name="active">True</field>
    </record>

</odoo>
