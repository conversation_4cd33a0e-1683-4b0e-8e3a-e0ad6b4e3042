# -*- coding: utf-8 -*-
{
    "name": "Hotel Management",
    "summary": """
            core modules for hotel management
        """,
    "description": """
           """,
    "author": "Trigonal Technology",
    "website": "https://trigonaltechnology.com/",
    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/18.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    "category": "Sales",
    "version": "0.1",
    "sequence": -101,
    # any module necessary for this one to work correctly
    "depends": [
        "web",
        "account",
        "base_accounting_kit",
        "l10n_np",
        "sale_management",
        "stock",
        "sale_stock",
        "hotel_company_info",
        "board",
    ],
    # always loaded
    "data": [
        "security/hotel_security.xml",
        "security/record_rule.xml",
        "security/ir.model.access.csv",
        "data/hotel_reservation_seq.xml",
        "views/hotel_amenities.xml",
        "views/hotel_services.xml",
        "views/hotel_bed_type.xml",
        "data/hotel_bed_type.xml",
        "data/hotel_branch.xml",
        "data/hotel_data.xml",
        "data/hotel_floor_data.xml",
        "data/hotel_room_type_data.xml",
        "data/hotel_amenities_data.xml",
        "data/hotel_service_data.xml",
        "data/hotel_room_data.xml",
        "views/hotel_room.xml",
        "views/hotel_floor.xml",
        "wizard/hotel_config.xml",
        "views/product_view.xml",
        "views/action.xml",
        "views/top_list_room.xml",
        "views/top_list_customer.xml",
        "views/hotel_payment.xml",
        # "data/hotel_payment.xml",
        "views/menu.xml",
        "views/hotel_reservation.xml",
        "views/hotel_hotel.xml",
        "views/hotel_branch.xml",
        "views/sale.xml",
        "views/hotel_room_summary.xml",
        "views/hotel_board.xml",
        "views/hotel_advance_payment.xml",
    ],
    "assets": {
        "web.assets_backend": [
            # CSS
            "hotel_core/static/src/css/room_summary.css",
            # XML Templates
            "hotel_core/static/src/xml/hotel_room_summary.xml",
            # JS
            "hotel_core/static/src/js/hotel_room_summary.js",
            "hotel_core/static/src/js/hotel_dashboard.js",
            "hotel_core/static/src/xml/hotel_dashboard.xml",
            "hotel_core/static/src/css/hotel_dashboard.css",
        ],
    },
    "installable": True,
    "application": True,
}
