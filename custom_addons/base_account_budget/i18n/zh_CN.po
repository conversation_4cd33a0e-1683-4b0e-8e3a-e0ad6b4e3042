# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_account_budget
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-09 10:32+0000\n"
"PO-Revision-Date: 2022-03-10 08:50+0800\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"Language: zh_CN\n"
"X-Generator: Poedit 2.2\n"

#. module: base_account_budget
#: model_terms:ir.actions.act_window,help:base_account_budget.act_budget_view
msgid ""
"A budget is a forecast of your company's income and/or expenses\n"
"                expected for a period in the future. A budget is defined on some\n"
"                financial accounts and/or analytic accounts (that may represent\n"
"                projects, departments, categories of products, etc.)"
msgstr ""
"预算是对公司收入和/或支出的预测\n"
"预计在未来一段时间内。预算是在某些时间定义的\n"
"金融账户和/或分析账户（可能代表\n"
"项目、部门、产品类别等）"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__account_ids
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_budget_post_form
msgid "Accounts"
msgstr "科目"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__percentage
msgid "Achievement"
msgstr "达成"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_needaction
msgid "Action Needed"
msgstr "需要采取的行动"

#. module: base_account_budget
#: model:ir.model,name:base_account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__analytic_account_id
msgid "Analytic Account"
msgstr "辅助核算"

#. module: base_account_budget
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr "批准"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: base_account_budget
#: model:ir.model,name:base_account_budget.model_budget_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__budget_id
#: model_terms:ir.ui.view,arch_db:base_account_budget.budget_budget_view_tree
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "Budget"
msgstr "预算"

#. module: base_account_budget
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr "预算项目"

#. module: base_account_budget
#: model:ir.model,name:base_account_budget.model_budget_lines
msgid "Budget Line"
msgstr "预算明细"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_analytic_account__budget_line
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__budget_line
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__budget_line
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_budget_line_form
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_budget_line_search
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_budget_line_tree
msgid "Budget Lines"
msgstr "预算明细"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__name
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "Budget Name"
msgstr "预算名称"

#. module: base_account_budget
#: model:ir.model,name:base_account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__general_budget_id
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_budget_post_form
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_budget_post_search
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr "预算状况"

#. module: base_account_budget
#: model:ir.actions.act_window,name:base_account_budget.open_budget_post_form
#: model:ir.ui.menu,name:base_account_budget.menu_budget_post_form
msgid "Budgetary Positions"
msgstr "预算状况"

#. module: base_account_budget
#: model:ir.actions.act_window,name:base_account_budget.act_budget_lines_view
#: model:ir.actions.act_window,name:base_account_budget.act_budget_view
#: model:ir.ui.menu,name:base_account_budget.menu_act_budget_view
#: model:ir.ui.menu,name:base_account_budget.menu_act_crossovered_budget_lines_view
msgid "Budgets"
msgstr "预算"

#. module: base_account_budget
#: model_terms:ir.actions.act_window,help:base_account_budget.act_budget_view
msgid ""
"By keeping track of where your money goes, you may be less\n"
"                likely to overspend, and more likely to meet your financial\n"
"                goals. Forecast a budget by detailing the expected revenue per\n"
"                analytic account and monitor its evolution based on the actuals\n"
"                realised during that period."
msgstr ""
"通过跟踪资金流向，您可能会减少\n"
"可能会超支，更可能满足您的财务要求\n"
"目标。通过详细说明每个月的预期收入来预测预算\n"
"分析帐户并根据实际情况监控调整以达到预算目的。"

#. module: base_account_budget
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr "取消预算"

#. module: base_account_budget
#: model:ir.model.fields.selection,name:base_account_budget.selection__budget_budget__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: base_account_budget
#: model_terms:ir.actions.act_window,help:base_account_budget.act_budget_view
msgid "Click to create a new budget."
msgstr "单击以创建新预算"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__company_id
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__company_id
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__company_id
msgid "Company"
msgstr "公司"

#. module: base_account_budget
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr "审核"

#. module: base_account_budget
#: model:ir.model.fields.selection,name:base_account_budget.selection__budget_budget__state__confirm
msgid "Confirmed"
msgstr "已审核"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__create_uid
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__create_uid
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__create_uid
msgid "Created by"
msgstr "创建人"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__create_date
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__create_date
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__create_date
msgid "Created on"
msgstr "创建于"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__display_name
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__display_name
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: base_account_budget
#: model:ir.model.fields.selection,name:base_account_budget.selection__budget_budget__state__done
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "Done"
msgstr "完成"

#. module: base_account_budget
#: model:ir.model.fields.selection,name:base_account_budget.selection__budget_budget__state__draft
msgid "Draft"
msgstr "草稿"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__date_to
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__date_to
msgid "End Date"
msgstr "结束时间"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_follower_ids
msgid "Followers"
msgstr "相关人员"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_channel_ids
msgid "Followers (Channels)"
msgstr "相关人员(频道)"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_partner_ids
msgid "Followers (Partners)"
msgstr "相关人员(业务伙伴)"

#. module: base_account_budget
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "From"
msgstr "来自"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_analytic_account__id
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__id
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__id
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__id
msgid "ID"
msgstr "ID"

#. module: base_account_budget
#: model:ir.model.fields,help:base_account_budget.field_budget_budget__message_needaction
#: model:ir.model.fields,help:base_account_budget.field_budget_budget__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 将会提示消息。"

#. module: base_account_budget
#: model:ir.model.fields,help:base_account_budget.field_budget_budget__message_has_error
#: model:ir.model.fields,help:base_account_budget.field_budget_budget__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_is_follower
msgid "Is Follower"
msgstr "是否相关人员"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_analytic_account____last_update
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post____last_update
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget____last_update
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__write_uid
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__write_uid
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__write_date
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__write_date
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__write_date
msgid "Last Updated on"
msgstr "最后更新日期"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_ids
msgid "Messages"
msgstr "消息"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_account_budget_post__name
msgid "Name"
msgstr "名称"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_needaction_counter
msgid "Number of Actions"
msgstr "执行次数"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: base_account_budget
#: model:ir.model.fields,help:base_account_budget.field_budget_budget__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要处理的消息数量"

#. module: base_account_budget
#: model:ir.model.fields,help:base_account_budget.field_budget_budget__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息数量"

#. module: base_account_budget
#: model:ir.model.fields,help:base_account_budget.field_budget_budget__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__paid_date
msgid "Paid Date"
msgstr "付款日期"

#. module: base_account_budget
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "Period"
msgstr "期间"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__planned_amount
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "Planned Amount"
msgstr "预算金额"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__practical_amount
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr "实际金额"

#. module: base_account_budget
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr "重置为草稿"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__creating_user_id
msgid "Responsible"
msgstr "负责人"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__date_from
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__date_from
msgid "Start Date"
msgstr "开始日期"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__state
msgid "Status"
msgstr "状态"

#. module: base_account_budget
#: code:addons/base_account_budget/models/account_budget.py:0
#, python-format
msgid "The budget must have at least one account."
msgstr "此预算必须至少需要有一个账户"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_lines__theoretical_amount
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:base_account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoretical Amount"
msgstr "理论金额"

#. module: base_account_budget
#: model_terms:ir.ui.view,arch_db:base_account_budget.crossovered_budget_view_form
msgid "To"
msgstr "按"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_unread
msgid "Unread Messages"
msgstr "未读邮件"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读邮件计数器"

#. module: base_account_budget
#: model:ir.model.fields.selection,name:base_account_budget.selection__budget_budget__state__validate
msgid "Validated"
msgstr "已验证"

#. module: base_account_budget
#: model:ir.model.fields,field_description:base_account_budget.field_budget_budget__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: base_account_budget
#: model:ir.model.fields,help:base_account_budget.field_budget_budget__website_message_ids
msgid "Website communication history"
msgstr "网站浏览历史"
